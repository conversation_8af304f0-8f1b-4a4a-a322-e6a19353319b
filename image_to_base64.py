"""
图片转Base64编码工具
支持多种图片格式：PNG, JPG, JPEG, GIF, BMP, WEBP等
提供多种使用方式：函数调用、命令行、GUI界面
"""

import base64
import os
import sys
from pathlib import Path
from typing import Optional, Union
import mimetypes

def image_to_base64(image_path: Union[str, Path], include_data_url: bool = True) -> Optional[str]:
    """
    将图片文件转换为Base64编码字符串
    
    Args:
        image_path: 图片文件路径
        include_data_url: 是否包含data URL前缀 (data:image/png;base64,)
    
    Returns:
        Base64编码字符串，失败返回None
    """
    try:
        # 转换为Path对象
        image_path = Path(image_path)
        
        # 检查文件是否存在
        if not image_path.exists():
            print(f"错误: 文件不存在 - {image_path}")
            return None
        
        # 检查是否为文件
        if not image_path.is_file():
            print(f"错误: 不是有效文件 - {image_path}")
            return None
        
        # 读取文件内容
        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
        
        # 转换为Base64
        base64_string = base64.b64encode(image_data).decode('utf-8')
        
        # 如果需要包含data URL前缀
        if include_data_url:
            # 获取MIME类型
            mime_type, _ = mimetypes.guess_type(str(image_path))
            if mime_type is None:
                # 根据文件扩展名推断MIME类型
                ext = image_path.suffix.lower()
                mime_map = {
                    '.png': 'image/png',
                    '.jpg': 'image/jpeg',
                    '.jpeg': 'image/jpeg',
                    '.gif': 'image/gif',
                    '.bmp': 'image/bmp',
                    '.webp': 'image/webp',
                    '.svg': 'image/svg+xml',
                    '.ico': 'image/x-icon'
                }
                mime_type = mime_map.get(ext, 'image/png')
            
            base64_string = f"data:{mime_type};base64,{base64_string}"
        
        return base64_string
        
    except Exception as e:
        print(f"错误: 转换失败 - {e}")
        return None

def base64_to_image(base64_string: str, output_path: Union[str, Path]) -> bool:
    """
    将Base64编码字符串转换回图片文件
    
    Args:
        base64_string: Base64编码字符串
        output_path: 输出文件路径
    
    Returns:
        成功返回True，失败返回False
    """
    try:
        # 如果包含data URL前缀，则移除
        if base64_string.startswith('data:'):
            base64_string = base64_string.split(',', 1)[1]
        
        # 解码Base64
        image_data = base64.b64decode(base64_string)
        
        # 写入文件
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'wb') as output_file:
            output_file.write(image_data)
        
        print(f"成功: 图片已保存到 {output_path}")
        return True
        
    except Exception as e:
        print(f"错误: 转换失败 - {e}")
        return False

def batch_convert_images(input_dir: Union[str, Path], output_file: Union[str, Path] = None) -> dict:
    """
    批量转换目录中的所有图片为Base64
    
    Args:
        input_dir: 输入目录路径
        output_file: 输出文件路径（可选）
    
    Returns:
        包含文件名和Base64编码的字典
    """
    input_dir = Path(input_dir)
    results = {}
    
    if not input_dir.exists() or not input_dir.is_dir():
        print(f"错误: 目录不存在 - {input_dir}")
        return results
    
    # 支持的图片格式
    image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.svg', '.ico'}
    
    # 遍历目录中的图片文件
    for file_path in input_dir.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            print(f"处理: {file_path.name}")
            base64_string = image_to_base64(file_path, include_data_url=True)
            if base64_string:
                results[file_path.name] = base64_string
    
    # 如果指定了输出文件，保存结果
    if output_file and results:
        output_file = Path(output_file)
        try:
            import json
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"批量转换结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存结果失败: {e}")
    
    return results

def get_image_info(image_path: Union[str, Path]) -> dict:
    """
    获取图片文件信息
    
    Args:
        image_path: 图片文件路径
    
    Returns:
        包含图片信息的字典
    """
    try:
        image_path = Path(image_path)
        
        if not image_path.exists():
            return {"error": "文件不存在"}
        
        # 基本文件信息
        file_size = image_path.stat().st_size
        file_ext = image_path.suffix.lower()
        
        # 计算Base64编码后的大小（约为原文件的4/3）
        base64_size = (file_size * 4 + 2) // 3
        
        info = {
            "文件名": image_path.name,
            "文件路径": str(image_path.absolute()),
            "文件大小": f"{file_size:,} bytes ({file_size/1024:.1f} KB)",
            "文件格式": file_ext,
            "Base64大小": f"{base64_size:,} bytes ({base64_size/1024:.1f} KB)"
        }
        
        # 尝试获取图片尺寸（需要PIL库）
        try:
            from PIL import Image
            with Image.open(image_path) as img:
                info["图片尺寸"] = f"{img.width} x {img.height}"
                info["颜色模式"] = img.mode
        except ImportError:
            info["注意"] = "安装PIL/Pillow库可获取更多图片信息"
        except Exception:
            pass
        
        return info
        
    except Exception as e:
        return {"error": str(e)}

def main():
    """命令行主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python image_to_base64.py <图片路径> [选项]")
        print("  python image_to_base64.py <目录路径> --batch [输出文件]")
        print("")
        print("选项:")
        print("  --no-data-url    不包含data URL前缀")
        print("  --output <文件>  保存到指定文件")
        print("  --info          显示图片信息")
        print("  --batch         批量处理目录")
        print("")
        print("示例:")
        print("  python image_to_base64.py image.png")
        print("  python image_to_base64.py image.jpg --output result.txt")
        print("  python image_to_base64.py ./images --batch results.json")
        return
    
    input_path = sys.argv[1]
    include_data_url = True
    output_file = None
    show_info = False
    batch_mode = False
    
    # 解析命令行参数
    i = 2
    while i < len(sys.argv):
        arg = sys.argv[i]
        if arg == '--no-data-url':
            include_data_url = False
        elif arg == '--output' and i + 1 < len(sys.argv):
            output_file = sys.argv[i + 1]
            i += 1
        elif arg == '--info':
            show_info = True
        elif arg == '--batch':
            batch_mode = True
            if i + 1 < len(sys.argv) and not sys.argv[i + 1].startswith('--'):
                output_file = sys.argv[i + 1]
                i += 1
        i += 1
    
    # 显示图片信息
    if show_info and not batch_mode:
        info = get_image_info(input_path)
        print("图片信息:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        print()
    
    # 批量处理模式
    if batch_mode:
        results = batch_convert_images(input_path, output_file)
        print(f"批量处理完成，共处理 {len(results)} 个文件")
        return
    
    # 单文件处理模式
    base64_string = image_to_base64(input_path, include_data_url)
    
    if base64_string:
        if output_file:
            # 保存到文件
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(base64_string)
                print(f"Base64编码已保存到: {output_file}")
            except Exception as e:
                print(f"保存失败: {e}")
        else:
            # 输出到控制台
            print("Base64编码:")
            print(base64_string)
    
    print(f"\nBase64字符串长度: {len(base64_string) if base64_string else 0}")

if __name__ == "__main__":
    res = image_to_base64(r"D:\resource\beike.png")
    with open("D:/base64.txt", "w") as f:
        f.write(res)
    # base64_to_image(res, "D:/resource/beike2.png")

