# 3ds Max 2023 Bounding Box Tool (PySide2版本)

专为3ds Max 2023设计的Python工具，使用PySide2创建现代化GUI界面。

## 🎯 功能特性

1. **✅ 选中模型显示BoundingBox** - 红色线框显示对象边界
2. **✅ 显示模型体积** - 绿色文本显示体积和尺寸信息  
3. **✅ Clear按钮** - 清除显示并注销所有callback
4. **✅ Display按钮** - 显示boundingbox和体积，并注册callback

## 🔧 技术说明

- **适用版本**: 3ds Max 2023+
- **语言**: Python 3.7+
- **核心模块**: `pymxs` (替代已移除的MaxPlus)
- **UI框架**: PySide2 (Qt for Python)
- **特色**: 现代化GUI界面，自定义样式

## 🎨 界面特点

### 现代化设计
- **美观按钮**: 自定义CSS样式，绿色/红色主题
- **清晰布局**: 分组显示，视觉层次分明
- **实时状态**: 彩色状态提示，操作反馈及时
- **置顶窗口**: 始终保持在3ds Max前方

### 用户体验
- **响应式**: 按钮悬停和点击效果
- **信息丰富**: 详细的对象信息显示
- **状态反馈**: 绿色成功，红色错误提示
- **固定尺寸**: 350x220像素，紧凑实用

## 📦 安装和使用

### 前置要求
确保3ds Max 2023中PySide2可用：
```python
# 在3ds Max Python编辑器中测试
from PySide2.QtWidgets import QApplication
print("PySide2 available!")
```

### 启动方法

#### 方法1: MAXScript命令
```maxscript
python.ExecuteFile "C:/path/to/bounding_box_tool_pyside2.py"
```

#### 方法2: Python编辑器
1. 打开 Scripting → Python Editor
2. 加载 `bounding_box_tool_pyside2.py`
3. 执行脚本 (Ctrl+E)

#### 方法3: 拖拽执行
直接将.py文件拖拽到3ds Max视窗中

## 🎮 使用指南

### 基本操作流程
1. **启动工具** → 弹出PySide2界面窗口
2. **选择对象** → 在3ds Max中选择要分析的模型
3. **显示边界框** → 点击绿色"Display BoundingBox"按钮
4. **查看信息** → 观察红色线框和绿色文本信息
5. **清除显示** → 点击红色"Clear Display"按钮

### 功能详解

#### 🟢 Display BoundingBox按钮
```python
# 执行的操作
- 计算选中对象的世界坐标边界框
- 创建红色线框Box helper
- 创建绿色文本显示体积信息
- 自动注册selection change callback
- 更新GUI界面信息
```

#### 🔴 Clear Display按钮
```python
# 执行的操作
- 删除所有创建的辅助对象
- 注销selection change callback
- 清空GUI界面信息
- 重置工具状态
```

## 📊 信息显示

### GUI界面信息
- **Object**: `Object: [对象名称]`
- **Volume**: `Volume: [数值] units³`
- **Dimensions**: `Dimensions: X x Y x Z`
- **Status**: 操作状态反馈

### 3D视窗显示
- **红色线框**: 精确的BoundingBox边界
- **绿色文本**: 体积和尺寸信息，位于对象上方

## 🔍 技术实现

### 核心计算
```python
# 世界坐标系边界框计算
bbox = rt.nodeGetBoundingBox(obj, rt.Matrix3(1))
min_point = bbox[0]
max_point = bbox[1]

# 体积计算
dimensions = [max_point[i] - min_point[i] for i in range(3)]
volume = dimensions[0] * dimensions[1] * dimensions[2]
```

### PySide2界面
```python
# 现代化按钮样式
self.display_button.setStyleSheet("""
    QPushButton {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 8px;
        font-weight: bold;
        border-radius: 4px;
    }
    QPushButton:hover {
        background-color: #45a049;
    }
""")
```

### Callback管理
```python
# 注册callback
rt.callbacks.addScript(rt.name("selectionSetChanged"), 
                      callback_script, 
                      id=rt.name("bboxToolCallback"))

# 注销callback  
rt.callbacks.removeScripts(id=rt.name("bboxToolCallback"))
```

## ⚠️ 注意事项

### 系统要求
1. **3ds Max版本**: 2023或更高版本
2. **Python环境**: 确保PySide2模块可用
3. **权限**: 需要创建和删除场景对象的权限

### 使用建议
1. **对象选择**: 工具处理第一个选中的对象
2. **复杂场景**: 大量对象时建议手动控制更新
3. **清理习惯**: 使用完毕后点击Clear按钮清理
4. **窗口管理**: 工具窗口会保持在最前方

## 🐛 故障排除

### PySide2相关问题
```python
# 测试PySide2是否可用
try:
    from PySide2.QtWidgets import QApplication
    print("✅ PySide2 OK")
except ImportError:
    print("❌ PySide2 not available")
```

### 常见问题解决
1. **工具无法启动**: 检查PySide2模块
2. **界面显示异常**: 重新运行脚本
3. **Callback不工作**: 点击Display按钮重新注册
4. **对象创建失败**: 检查3ds Max场景权限

## 📝 代码结构

```
bounding_box_tool_pyside2.py
├── BoundingBoxToolWidget (PySide2界面类)
│   ├── init_ui() - 界面初始化
│   ├── display_bounding_box() - 显示功能
│   ├── clear_display() - 清除功能
│   ├── calculate_bounding_box() - 计算逻辑
│   ├── create_bounding_box_helper() - 创建线框
│   ├── create_volume_text() - 创建文本
│   ├── register_callback() - 注册回调
│   └── unregister_callback() - 注销回调
├── BoundingBoxTool (工具管理类)
└── start_bounding_box_tool() - 启动函数
```

## 🎨 界面预览

```
┌─────────────────────────────────────┐
│    Bounding Box & Volume Tool       │
├─────────────────────────────────────┤
│ Object: Box001                      │
│ Volume: 1000.00 units³              │
│ Dimensions: 10.00 x 10.00 x 10.00   │
├─────────────────────────────────────┤
│ [    Display BoundingBox    ] 🟢    │
│ [      Clear Display        ] 🔴    │
├─────────────────────────────────────┤
│           Ready ✓                   │
└─────────────────────────────────────┘
```

## 🔄 更新日志

### v2.0 - PySide2版本
- ✨ 全新PySide2界面
- 🎨 现代化视觉设计
- 🔧 优化的用户体验
- 📱 响应式按钮效果
- 🎯 更好的状态反馈

### v1.0 - Tkinter版本
- 🚀 基础功能实现
- 📦 pymxs集成
- 🔄 Callback管理

---

**享受现代化的3ds Max工具体验！** 🚀
