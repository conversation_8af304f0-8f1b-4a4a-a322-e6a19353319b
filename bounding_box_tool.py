"""
3ds Max Bounding Box Tool for 3ds Max 2023
Author: Assistant
Description: Python tool to display bounding box and volume for selected objects
Note: Uses pymxs instead of MaxPlus (which was removed in 3ds Max 2023)
"""

import pymxs
from pymxs import runtime as rt
import tkinter as tk
from tkinter import ttk
import threading

class BoundingBoxTool:
    def __init__(self):
        self.bounding_box_helper = None
        self.volume_text = None
        self.callback_registered = False
        self.callback_id = None
        
        # Create GUI
        self.create_gui()
        
    def create_gui(self):
        """Create the main GUI window"""
        self.root = tk.Tk()
        self.root.title("Bounding Box Tool - 3ds Max 2023")
        self.root.geometry("350x200")
        self.root.resizable(False, False)
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Bounding Box & Volume Tool", 
                               font=("Arial", 12, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # Object info
        self.object_label = ttk.Label(main_frame, text="Object: None selected")
        self.object_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=2)
        
        self.volume_label = ttk.Label(main_frame, text="Volume: 0.0 units³")
        self.volume_label.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=2)
        
        self.dimensions_label = ttk.Label(main_frame, text="Dimensions: 0 x 0 x 0")
        self.dimensions_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=2)
        
        # Separator
        separator = ttk.Separator(main_frame, orient='horizontal')
        separator.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # Buttons
        self.display_button = ttk.Button(main_frame, text="Display BoundingBox", 
                                        command=self.display_bounding_box)
        self.display_button.grid(row=5, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))
        
        self.clear_button = ttk.Button(main_frame, text="Clear Display", 
                                      command=self.clear_display)
        self.clear_button.grid(row=6, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))
        
        # Status
        self.status_label = ttk.Label(main_frame, text="Ready", foreground="green")
        self.status_label.grid(row=7, column=0, columnspan=2, pady=(10, 0))
        
        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def get_selected_object(self):
        """Get the first selected object"""
        try:
            selection = rt.selection
            if len(selection) > 0:
                return selection[0]
            return None
        except Exception as e:
            print(f"Error getting selection: {e}")
            return None
    
    def calculate_bounding_box(self, obj):
        """Calculate bounding box for the given object"""
        try:
            # Get bounding box in world space
            bbox = rt.nodeGetBoundingBox(obj, rt.Matrix3(1))
            min_point = bbox[0]
            max_point = bbox[1]
            
            # Calculate center and dimensions
            center = [(min_point[i] + max_point[i]) / 2 for i in range(3)]
            dimensions = [max_point[i] - min_point[i] for i in range(3)]
            
            # Calculate volume
            volume = dimensions[0] * dimensions[1] * dimensions[2]
            
            return {
                'min': min_point,
                'max': max_point,
                'center': center,
                'dimensions': dimensions,
                'volume': volume
            }
        except Exception as e:
            print(f"Error calculating bounding box: {e}")
            return None
    
    def create_bounding_box_helper(self, bbox_info):
        """Create a box helper to visualize the bounding box"""
        try:
            # Delete existing helper
            if self.bounding_box_helper:
                rt.delete(self.bounding_box_helper)
            
            # Create box helper
            box = rt.Box()
            box.length = bbox_info['dimensions'][0]
            box.width = bbox_info['dimensions'][1]
            box.height = bbox_info['dimensions'][2]
            box.pos = rt.Point3(bbox_info['center'][0], bbox_info['center'][1], bbox_info['center'][2])
            box.wirecolor = rt.Color(255, 0, 0)  # Red color
            box.boxmode = True
            box.name = "BoundingBox_Helper"
            box.renderable = False
            
            self.bounding_box_helper = box
            return box
            
        except Exception as e:
            print(f"Error creating bounding box helper: {e}")
            return None
    
    def create_volume_text(self, obj, bbox_info):
        """Create text object to display volume information"""
        try:
            # Delete existing text
            if self.volume_text:
                rt.delete(self.volume_text)
            
            # Create text object
            text_obj = rt.Text()
            text_obj.text = f"Volume: {bbox_info['volume']:.2f} units³\nDimensions: {bbox_info['dimensions'][0]:.2f} x {bbox_info['dimensions'][1]:.2f} x {bbox_info['dimensions'][2]:.2f}"
            text_obj.size = 10
            text_obj.pos = rt.Point3(bbox_info['center'][0], bbox_info['center'][1], bbox_info['max'][2] + 20)
            text_obj.wirecolor = rt.Color(0, 255, 0)  # Green color
            text_obj.name = "Volume_Text"
            
            self.volume_text = text_obj
            return text_obj
            
        except Exception as e:
            print(f"Error creating volume text: {e}")
            return None
    
    def update_gui_info(self, obj, bbox_info):
        """Update GUI with object information"""
        try:
            if obj and bbox_info:
                self.object_label.config(text=f"Object: {obj.name}")
                self.volume_label.config(text=f"Volume: {bbox_info['volume']:.2f} units³")
                self.dimensions_label.config(text=f"Dimensions: {bbox_info['dimensions'][0]:.2f} x {bbox_info['dimensions'][1]:.2f} x {bbox_info['dimensions'][2]:.2f}")
            else:
                self.object_label.config(text="Object: None selected")
                self.volume_label.config(text="Volume: 0.0 units³")
                self.dimensions_label.config(text="Dimensions: 0 x 0 x 0")
        except Exception as e:
            print(f"Error updating GUI: {e}")
    
    def display_bounding_box(self):
        """Display bounding box and volume for selected object"""
        try:
            obj = self.get_selected_object()
            if not obj:
                self.status_label.config(text="No object selected", foreground="red")
                self.update_gui_info(None, None)
                return
            
            # Calculate bounding box
            bbox_info = self.calculate_bounding_box(obj)
            if not bbox_info:
                self.status_label.config(text="Error calculating bounding box", foreground="red")
                return
            
            # Create visual helpers
            self.create_bounding_box_helper(bbox_info)
            self.create_volume_text(obj, bbox_info)
            
            # Update GUI
            self.update_gui_info(obj, bbox_info)
            
            # Register callback if not already registered
            if not self.callback_registered:
                self.register_callback()
            
            self.status_label.config(text="Bounding box displayed", foreground="green")
            
        except Exception as e:
            print(f"Error in display_bounding_box: {e}")
            self.status_label.config(text="Error displaying bounding box", foreground="red")
    
    def clear_display(self):
        """Clear all display elements and unregister callbacks"""
        try:
            # Delete helpers
            if self.bounding_box_helper:
                rt.delete(self.bounding_box_helper)
                self.bounding_box_helper = None
            
            if self.volume_text:
                rt.delete(self.volume_text)
                self.volume_text = None
            
            # Unregister callback
            self.unregister_callback()
            
            # Update GUI
            self.update_gui_info(None, None)
            self.status_label.config(text="Display cleared", foreground="green")
            
        except Exception as e:
            print(f"Error in clear_display: {e}")
            self.status_label.config(text="Error clearing display", foreground="red")
    
    def register_callback(self):
        """Register selection change callback"""
        try:
            if not self.callback_registered:
                # Create callback script
                callback_script = """
                python.Execute("
try:
    if 'bbox_tool' in globals() and bbox_tool:
        bbox_tool.on_selection_changed()
except:
    pass
")
                """
                
                rt.callbacks.addScript(rt.name("selectionSetChanged"), callback_script, id=rt.name("bboxToolCallback"))
                self.callback_registered = True
                print("Callback registered")
                
        except Exception as e:
            print(f"Error registering callback: {e}")
    
    def unregister_callback(self):
        """Unregister selection change callback"""
        try:
            if self.callback_registered:
                rt.callbacks.removeScripts(id=rt.name("bboxToolCallback"))
                self.callback_registered = False
                print("Callback unregistered")
                
        except Exception as e:
            print(f"Error unregistering callback: {e}")
    
    def on_selection_changed(self):
        """Handle selection change event"""
        try:
            obj = self.get_selected_object()
            if obj:
                bbox_info = self.calculate_bounding_box(obj)
                if bbox_info:
                    self.create_bounding_box_helper(bbox_info)
                    self.create_volume_text(obj, bbox_info)
                    self.update_gui_info(obj, bbox_info)
            else:
                # Clear display if no selection
                if self.bounding_box_helper:
                    rt.delete(self.bounding_box_helper)
                    self.bounding_box_helper = None
                if self.volume_text:
                    rt.delete(self.volume_text)
                    self.volume_text = None
                self.update_gui_info(None, None)
                
        except Exception as e:
            print(f"Error in selection change callback: {e}")
    
    def on_closing(self):
        """Handle window closing"""
        self.clear_display()
        self.root.destroy()
    
    def run(self):
        """Start the tool"""
        self.root.mainloop()

# Global variable to hold the tool instance
bbox_tool = None

def start_bounding_box_tool():
    """Function to start the bounding box tool"""
    global bbox_tool
    
    # Close existing tool if running
    if bbox_tool:
        try:
            bbox_tool.clear_display()
            bbox_tool.root.destroy()
        except:
            pass
    
    # Create and start new tool
    bbox_tool = BoundingBoxTool()
    bbox_tool.run()

# Start the tool
if __name__ == "__main__":
    start_bounding_box_tool()
else:
    # When imported in 3ds Max
    start_bounding_box_tool()
