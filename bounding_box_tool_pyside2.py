"""
3ds Max Bounding Box Tool for 3ds Max 2023
Author: Assistant
Description: Python tool to display bounding box and volume for selected objects
Note: Uses pymxs instead of MaxPlus (which was removed in 3ds Max 2023)
UI: PySide2
"""

import pymxs
from pymxs import runtime as rt
from PySide2.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
                               QLabel, QPushButton, QFrame, QSizePolicy)
from PySide2.QtCore import Qt, QTimer
from PySide2.QtGui import QFont
import sys

class BoundingBoxToolWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.bounding_box_helper = None
        self.volume_text = None
        self.callback_registered = False
        
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Bounding Box Tool - 3ds Max 2023")
        self.setFixedSize(350, 220)
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # Title
        title_label = QLabel("Bounding Box & Volume Tool")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # Separator
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.HLine)
        separator1.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator1)
        
        # Object information
        self.object_label = QLabel("Object: None selected")
        self.object_label.setStyleSheet("color: #333333; font-weight: bold;")
        main_layout.addWidget(self.object_label)
        
        self.volume_label = QLabel("Volume: 0.0 units³")
        self.volume_label.setStyleSheet("color: #0066cc;")
        main_layout.addWidget(self.volume_label)
        
        self.dimensions_label = QLabel("Dimensions: 0 x 0 x 0")
        self.dimensions_label.setStyleSheet("color: #0066cc;")
        main_layout.addWidget(self.dimensions_label)
        
        # Separator
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.HLine)
        separator2.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator2)
        
        # Buttons
        self.display_button = QPushButton("Display BoundingBox")
        self.display_button.clicked.connect(self.display_bounding_box)
        main_layout.addWidget(self.display_button)
        
        self.clear_button = QPushButton("Clear Display")
        self.clear_button.clicked.connect(self.clear_display)
        main_layout.addWidget(self.clear_button)
        
        # Status
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: #008000; font-style: italic;")
        self.status_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.status_label)
        
        self.setLayout(main_layout)
        
    def get_selected_object(self):
        """Get the first selected object"""
        try:
            selection = rt.selection
            if len(selection) > 0:
                return selection[0]
            return None
        except Exception as e:
            print(f"Error getting selection: {e}")
            return None
    
    def calculate_bounding_box(self, obj):
        """Calculate bounding box for the given object"""
        try:
            # Get bounding box in world space
            bbox = rt.nodeGetBoundingBox(obj, obj.transform)
            min_point = bbox[0]
            max_point = bbox[1]
            
            # Calculate center and dimensions
            # center = [(min_point[i] + max_point[i]) / 2 for i in range(3)]
            center = obj.pos[0], obj.pos[1], obj.min[2]
            dimensions = [max_point[i] - min_point[i] for i in range(3)]
            
            # Calculate volume
            volume = dimensions[0] * dimensions[1] * dimensions[2]
            
            return {
                'min': min_point,
                'max': max_point,
                'center': center,
                'dimensions': dimensions,
                'volume': volume
            }
        except Exception as e:
            print(f"Error calculating bounding box: {e}")
            return None
    
    def create_bounding_box_helper(self, bbox_info, obj):
        """Create a box helper to visualize the bounding box"""
        try:
            # Delete existing helper
            if self.bounding_box_helper:
                rt.delete(self.bounding_box_helper)
            
            # Create box helper
            box = rt.Box()
            box.width = bbox_info['dimensions'][0]
            box.length = bbox_info['dimensions'][1]
            box.height = bbox_info['dimensions'][2]
            box.rotation = obj.rotation
            box.pos = rt.Point3(bbox_info['center'][0], bbox_info['center'][1], bbox_info['center'][2])
            box.wirecolor = rt.Color(255, 0, 0)  # Red color
            box.boxmode = True
            box.name = "BoundingBox_Helper"
            box.renderable = False
            
            self.bounding_box_helper = box
            return box
            
        except Exception as e:
            print(f"Error creating bounding box helper: {e}")
            return None
    
    def create_volume_text(self, obj, bbox_info):
        """Create text object to display volume information"""
        try:
            # Delete existing text
            if self.volume_text:
                rt.delete(self.volume_text)
            
            # Create text object
            text_obj = rt.Text()
            text_obj.text = f"Volume: {bbox_info['volume']:.2f} units³\nDimensions: {bbox_info['dimensions'][0]:.2f} x {bbox_info['dimensions'][1]:.2f} x {bbox_info['dimensions'][2]:.2f}"
            text_obj.size = 10
            text_obj.pos = rt.Point3(bbox_info['center'][0], bbox_info['center'][1], bbox_info['max'][2] + 20)
            text_obj.wirecolor = rt.Color(0, 255, 0)  # Green color
            text_obj.name = "Volume_Text"
            
            self.volume_text = text_obj
            return text_obj
            
        except Exception as e:
            print(f"Error creating volume text: {e}")
            return None
    
    def update_gui_info(self, obj, bbox_info):
        """Update GUI with object information"""
        try:
            if obj and bbox_info:
                self.object_label.setText(f"Object: {obj.name}")
                self.volume_label.setText(f"Volume: {bbox_info['volume']:.2f} units³")
                self.dimensions_label.setText(f"Dimensions: {bbox_info['dimensions'][0]:.2f} x {bbox_info['dimensions'][1]:.2f} x {bbox_info['dimensions'][2]:.2f}")
            else:
                self.object_label.setText("Object: None selected")
                self.volume_label.setText("Volume: 0.0 units³")
                self.dimensions_label.setText("Dimensions: 0 x 0 x 0")
        except Exception as e:
            print(f"Error updating GUI: {e}")
    
    def display_bounding_box(self):
        """Display bounding box and volume for selected object"""
        try:
            obj = self.get_selected_object()
            if not obj:
                self.status_label.setText("No object selected")
                self.status_label.setStyleSheet("color: #ff0000; font-style: italic;")
                self.update_gui_info(None, None)
                return
            
            # Calculate bounding box
            bbox_info = self.calculate_bounding_box(obj)
            if not bbox_info:
                self.status_label.setText("Error calculating bounding box")
                self.status_label.setStyleSheet("color: #ff0000; font-style: italic;")
                return
            
            # Create visual helpers
            self.create_bounding_box_helper(bbox_info, obj)
            self.create_volume_text(obj, bbox_info)
            
            # Update GUI
            self.update_gui_info(obj, bbox_info)
            
            # Register callback if not already registered
            if not self.callback_registered:
                self.register_callback()
            
            self.status_label.setText("Bounding box displayed")
            self.status_label.setStyleSheet("color: #008000; font-style: italic;")
            
        except Exception as e:
            print(f"Error in display_bounding_box: {e}")
            self.status_label.setText("Error displaying bounding box")
            self.status_label.setStyleSheet("color: #ff0000; font-style: italic;")

        finally:
            rt.redrawViews()

    def clear_display(self):
        """Clear all display elements and unregister callbacks"""
        try:
            # Delete helpers
            if self.bounding_box_helper:
                rt.delete(self.bounding_box_helper)
                self.bounding_box_helper = None
            
            if self.volume_text:
                rt.delete(self.volume_text)
                self.volume_text = None
            
            # Unregister callback
            self.unregister_callback()
            
            # Update GUI
            self.update_gui_info(None, None)
            self.status_label.setText("Display cleared")
            self.status_label.setStyleSheet("color: #008000; font-style: italic;")
            
        except Exception as e:
            print(f"Error in clear_display: {e}")
            self.status_label.setText("Error clearing display")
            self.status_label.setStyleSheet("color: #ff0000; font-style: italic;")
    
    def register_callback(self):
        """Register selection change callback"""
        try:
            if not self.callback_registered:
                # Create callback script
                callback_script = """
                python.Execute("
try:
    if 'bbox_tool_widget' in globals() and bbox_tool_widget:
        bbox_tool_widget.on_selection_changed()
except:
    pass
")
                """
                
                rt.callbacks.addScript(rt.name("selectionSetChanged"), callback_script, id=rt.name("bboxToolCallback"))
                self.callback_registered = True
                print("Callback registered")
                
        except Exception as e:
            print(f"Error registering callback: {e}")
    
    def unregister_callback(self):
        """Unregister selection change callback"""
        try:
            if self.callback_registered:
                rt.callbacks.removeScripts(id=rt.name("bboxToolCallback"))
                self.callback_registered = False
                print("Callback unregistered")
                
        except Exception as e:
            print(f"Error unregistering callback: {e}")
    
    def on_selection_changed(self):
        """Handle selection change event"""
        try:
            obj = self.get_selected_object()
            if obj:
                bbox_info = self.calculate_bounding_box(obj)
                if bbox_info:
                    self.create_bounding_box_helper(bbox_info)
                    self.create_volume_text(obj, bbox_info)
                    self.update_gui_info(obj, bbox_info)
            else:
                # Clear display if no selection
                if self.bounding_box_helper:
                    rt.delete(self.bounding_box_helper)
                    self.bounding_box_helper = None
                if self.volume_text:
                    rt.delete(self.volume_text)
                    self.volume_text = None
                self.update_gui_info(None, None)
                
        except Exception as e:
            print(f"Error in selection change callback: {e}")
    
    def closeEvent(self, event):
        """Handle window closing"""
        self.clear_display()
        event.accept()

class BoundingBoxTool:
    def __init__(self):
        self.app = None
        self.widget = None
        
    def show(self):
        """Show the tool"""
        # Check if QApplication exists
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        
        # Create and show widget
        self.widget = BoundingBoxToolWidget()
        self.widget.show()
        
        return self.widget

# Global variable to hold the tool instance
bbox_tool = None
bbox_tool_widget = None

def start_bounding_box_tool():
    """Function to start the bounding box tool"""
    global bbox_tool, bbox_tool_widget
    
    # Close existing tool if running
    if bbox_tool_widget:
        try:
            bbox_tool_widget.clear_display()
            bbox_tool_widget.close()
        except:
            pass
    
    # Create and start new tool
    bbox_tool = BoundingBoxTool()
    bbox_tool_widget = bbox_tool.show()
    
    print("Bounding Box Tool (PySide2) started successfully!")
    return bbox_tool_widget

# Start the tool
if __name__ == "__main__":
    start_bounding_box_tool()
else:
    # When imported in 3ds Max
    start_bounding_box_tool()
