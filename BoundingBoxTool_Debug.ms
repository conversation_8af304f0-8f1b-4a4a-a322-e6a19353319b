-- 3ds Max Bounding Box Tool (Debug Version with Multiple Methods)
-- Author: Assistant
-- Description: Displays bounding box with multiple calculation methods

-- Global variables
global boundingBoxHelper = undefined
global volumeText = undefined
global bbTool_rollout
global bbToolCallbackActive = false
global currentMethod = 1

-- Function to calculate volume from bounding box
function calculateVolume obj method:1 =
(
    if obj != undefined then
    (
        local bbox
        case method of
        (
            1: bbox = nodeGetBoundingBox obj (matrix3 1)  -- World space
            2: bbox = nodeGetBoundingBox obj obj.transform  -- Object space
            3: (
                -- Using object's min/max properties
                local objMin = obj.min
                local objMax = obj.max
                bbox = #([objMin.x, objMin.y, objMin.z], [objMax.x, objMax.y, objMax.z])
            )
            default: bbox = nodeGetBoundingBox obj (matrix3 1)
        )
        
        local dimensions = bbox[2] - bbox[1]
        local volume = dimensions.x * dimensions.y * dimensions.z
        return volume
    )
    else
        return 0.0
)

-- Function to create bounding box visualization
function createBoundingBoxHelper obj method:1 =
(
    if obj != undefined then
    (
        if boundingBoxHelper != undefined then
            delete boundingBoxHelper
        
        local bbox
        local methodName = ""
        
        case method of
        (
            1: (
                bbox = nodeGetBoundingBox obj (matrix3 1)
                methodName = "World Space"
            )
            2: (
                bbox = nodeGetBoundingBox obj obj.transform
                methodName = "Object Transform"
            )
            3: (
                local objMin = obj.min
                local objMax = obj.max
                bbox = #([objMin.x, objMin.y, objMin.z], [objMax.x, objMax.y, objMax.z])
                methodName = "Min/Max Properties"
            )
            4: (
                -- Alternative method using mesh bounds
                local mesh_bbox = meshop.getBoundingBox obj.mesh
                local tm = obj.objectTransform
                local min_pt = mesh_bbox[1] * tm
                local max_pt = mesh_bbox[2] * tm
                bbox = #(min_pt, max_pt)
                methodName = "Mesh Bounds"
            )
            default: (
                bbox = nodeGetBoundingBox obj (matrix3 1)
                methodName = "Default World"
            )
        )
        
        local center = (bbox[1] + bbox[2]) / 2
        local dimensions = bbox[2] - bbox[1]
        
        -- Debug output
        print ("=== Method " + method as string + " (" + methodName + ") ===")
        print ("BBox Min: " + bbox[1] as string)
        print ("BBox Max: " + bbox[2] as string)
        print ("Center: " + center as string)
        print ("Dimensions: " + dimensions as string)
        print ("Object Pos: " + obj.pos as string)
        print ("Object Transform: " + obj.transform as string)
        
        boundingBoxHelper = Box length:dimensions.x width:dimensions.y height:dimensions.z
        boundingBoxHelper.pos = center
        boundingBoxHelper.wirecolor = color 255 0 0
        boundingBoxHelper.boxmode = true
        boundingBoxHelper.name = "BoundingBox_Helper_Method" + method as string
        boundingBoxHelper.renderable = false
        
        return boundingBoxHelper
    )
    return undefined
)

-- Function to create volume text display
function createVolumeText obj method:1 =
(
    if obj != undefined then
    (
        if volumeText != undefined then
            delete volumeText
        
        local volume = calculateVolume obj method:method
        local bbox
        
        case method of
        (
            1: bbox = nodeGetBoundingBox obj (matrix3 1)
            2: bbox = nodeGetBoundingBox obj obj.transform
            3: (
                local objMin = obj.min
                local objMax = obj.max
                bbox = #([objMin.x, objMin.y, objMin.z], [objMax.x, objMax.y, objMax.z])
            )
            default: bbox = nodeGetBoundingBox obj (matrix3 1)
        )
        
        local center = (bbox[1] + bbox[2]) / 2
        local dimensions = bbox[2] - bbox[1]
        
        volumeText = Text()
        local volumeStr = volume as string
        local dimXStr = dimensions.x as string
        local dimYStr = dimensions.y as string
        local dimZStr = dimensions.z as string
        
        volumeText.text = "Method " + method as string + "\nVolume: " + volumeStr + " units^3\nDimensions: " + dimXStr + " x " + dimYStr + " x " + dimZStr
        volumeText.size = 8
        volumeText.pos = [center.x, center.y, bbox[2].z + 20]
        volumeText.wirecolor = color 0 255 0
        volumeText.name = "Volume_Text_Method" + method as string
        
        return volumeText
    )
    return undefined
)

-- Function to update display for current selection
function updateBoundingBoxDisplay method:1 =
(
    if selection.count > 0 then
    (
        local obj = selection[1]
        createBoundingBoxHelper obj method:method
        createVolumeText obj method:method
        
        if bbTool_rollout != undefined then
        (
            local volume = calculateVolume obj method:method
            local bbox
            
            case method of
            (
                1: bbox = nodeGetBoundingBox obj (matrix3 1)
                2: bbox = nodeGetBoundingBox obj obj.transform
                3: (
                    local objMin = obj.min
                    local objMax = obj.max
                    bbox = #([objMin.x, objMin.y, objMin.z], [objMax.x, objMax.y, objMax.z])
                )
                default: bbox = nodeGetBoundingBox obj (matrix3 1)
            )
            
            local dimensions = bbox[2] - bbox[1]
            
            bbTool_rollout.lblObjectName.text = "Object: " + obj.name
            bbTool_rollout.lblVolume.text = "Volume: " + (formattedPrint volume format:"f") + " units^3"
            bbTool_rollout.lblDimensions.text = "Dimensions: " + 
                (formattedPrint dimensions.x format:"f") + " x " + 
                (formattedPrint dimensions.y format:"f") + " x " + 
                (formattedPrint dimensions.z format:"f")
            bbTool_rollout.lblMethod.text = "Method: " + method as string
        )
    )
    else
    (
        if boundingBoxHelper != undefined then
        (
            delete boundingBoxHelper
            boundingBoxHelper = undefined
        )
        if volumeText != undefined then
        (
            delete volumeText
            volumeText = undefined
        )
        
        if bbTool_rollout != undefined then
        (
            bbTool_rollout.lblObjectName.text = "Object: None selected"
            bbTool_rollout.lblVolume.text = "Volume: 0.0 units^3"
            bbTool_rollout.lblDimensions.text = "Dimensions: 0 x 0 x 0"
            bbTool_rollout.lblMethod.text = "Method: " + method as string
        )
    )
)

-- Function to clear all helpers
function clearBoundingBoxDisplay =
(
    if boundingBoxHelper != undefined then
    (
        delete boundingBoxHelper
        boundingBoxHelper = undefined
    )
    if volumeText != undefined then
    (
        delete volumeText
        volumeText = undefined
    )
    print "Display cleared"
)

-- Function to register callback
function registerCallback =
(
    if not bbToolCallbackActive then
    (
        callbacks.addScript #selectionSetChanged "if bbTool_rollout != undefined and bbTool_rollout.chkAutoUpdate.checked then updateBoundingBoxDisplay method:currentMethod" id:#bbToolCallback
        bbToolCallbackActive = true
        print "Callback registered"
    )
)

-- Function to unregister callback
function unregisterCallback =
(
    if bbToolCallbackActive then
    (
        callbacks.removeScripts id:#bbToolCallback
        bbToolCallbackActive = false
        print "Callback unregistered"
    )
)
