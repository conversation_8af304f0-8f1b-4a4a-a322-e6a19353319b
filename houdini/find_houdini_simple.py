# Minimal helper: find <PERSON><PERSON><PERSON> install path by version (Windows)
# No type hints, no extra helpers

def find_houdini_path(version):
    import os, re, platform
    try:
        import winreg
    except Exception:
        return None
    if platform.system().lower() != 'windows':
        return None

    version = (version or '').strip()
    vendor_keys = [r"Software\\Side Effects Software", r"Software\\WOW6432Node\\Side Effects Software"]
    value_names = ("InstallPath", "InstallFolder", "Path", "Location")
    results = []  # (major, minor, patch, path)

    for hive in (winreg.HKEY_LOCAL_MACHINE, winreg.HKEY_CURRENT_USER):
        for base in vendor_keys:
            try:
                with winreg.OpenKey(hive, base) as root:
                    i = 0
                    while True:
                        try:
                            sub = winreg.EnumKey(root, i); i += 1
                        except OSError:
                            break
                        if not sub.lower().startswith('houdini'):
                            continue
                        key = base + "\\" + sub
                        # read values
                        vals = {}
                        try:
                            with winreg.OpenKey(hive, key) as k:
                                j = 0
                                while True:
                                    try:
                                        name, data, _ = winreg.EnumValue(k, j)
                                        vals[name] = str(data)
                                        j += 1
                                    except OSError:
                                        break
                        except OSError:
                            pass
                        install_path = None
                        for n in value_names:
                            if n in vals:
                                install_path = vals[n]; break
                        if not install_path:
                            for nested in ("CurrentVersion", "InstallPath", "Settings"):
                                nkey = key + "\\" + nested
                                try:
                                    with winreg.OpenKey(hive, nkey) as nk:
                                        j = 0; tmp = {}
                                        while True:
                                            try:
                                                name, data, _ = winreg.EnumValue(nk, j)
                                                tmp[name] = str(data)
                                                j += 1
                                            except OSError:
                                                break
                                        for n in value_names:
                                            if n in tmp:
                                                install_path = tmp[n]; break
                                        if install_path:
                                            vals.update(tmp)
                                            break
                                except OSError:
                                    pass
                        if not install_path:
                            continue
                        ver = vals.get('Version') or sub.replace('Houdini', '').strip()
                        if not ver:
                            mver = re.search(r"\d+\.\d+(?:\.\d+)?", install_path)
                            ver = mver.group(0) if mver else ''
                        if ver.strip() == version:
                            return install_path
                        m = re.search(r"(\d+)\.(\d+)(?:\.(\d+))?", ver)
                        if m:
                            a = int(m.group(1)); b = int(m.group(2)); c = int(m.group(3) or '0')
                            results.append((a, b, c, install_path))
            except OSError:
                continue

    m = re.search(r"(\d+)\.(\d+)(?:\.(\d+))?", version)
    if m:
        ta = int(m.group(1)); tb = int(m.group(2))
        candidates = [r for r in results if r[0] == ta and r[1] == tb]
        if candidates:
            candidates.sort(reverse=True)
            return candidates[0][3]
    return None

