"""
find_houdini.py

Windows-only helper to locate installed Houdini paths via Windows Registry (and fallbacks).
Designed to be imported or executed from <PERSON><PERSON>'s package.py to set HFS/PATH.

Usage (Python):
    from houdini.find_houdini import find_latest_installation, find_installations
    latest = find_latest_installation()
    if latest:
        print(latest["version"], latest["install_path"])  # e.g. 20.5.293 C:\\Program Files\\Side Effects Software\\Houdini 20.5.293

CLI:
    python -m houdini.find_houdini --latest         # prints install path only
    python -m houdini.find_houdini --latest --json  # prints JSON with version/path
    python -m houdini.find_houdini --all --json     # list all installations as JSON

In Rez package.py (example):
    import json, subprocess, sys
    def commands():
        # Get latest Houdini install path
        out = subprocess.check_output([sys.executable, '-m', 'houdini.find_houdini', '--latest', '--json'], encoding='utf-8')
        info = json.loads(out)
        if not info:
            stop("No Houdini installation found.")
        hfs = info['install_path']
        env.HFS = hfs
        env.PATH.prepend(path.join(hfs, 'bin'))
        env.HOUDINI_PATH.prepend(hfs)
"""

from __future__ import annotations
import os
import re
import sys
import json
import platform
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

# winreg is Windows-only
try:
    import winreg  # type: ignore
except Exception:  # pragma: no cover
    winreg = None  # type: ignore

HOUDINI_VENDOR_KEY = r"Software\Side Effects Software"
POSSIBLE_VALUE_NAMES = ("InstallPath", "InstallFolder", "Path", "Location")

_version_re = re.compile(r"(\d+)(?:\.(\d+))?(?:\.(\d+))?")

@dataclass(order=True)
class HoudiniInstall:
    sort_key: Tuple[int, int, int]
    version: str
    install_path: str
    reg_hive: str = ""
    reg_key: str = ""

    @staticmethod
    def parse_version(s: str) -> Tuple[int, int, int]:
        """Parse a version like '20.5.293' or '19.5' to a numeric tuple for sorting.
        Unknown parts default to 0.
        """
        m = _version_re.search(s)
        if not m:
            return (0, 0, 0)
        parts = [int(p) if p is not None else 0 for p in m.groups()]
        # Ensure 3-length
        while len(parts) < 3:
            parts.append(0)
        return tuple(parts[:3])  # type: ignore


def _read_key_values(hive, subkey: str) -> Dict[str, str]:
    values: Dict[str, str] = {}
    if winreg is None:
        return values
    try:
        with winreg.OpenKey(hive, subkey) as k:
            # Enumerate values
            i = 0
            while True:
                try:
                    name, data, _ = winreg.EnumValue(k, i)
                    values[name] = str(data)
                    i += 1
                except OSError:
                    break
    except OSError:
        pass
    return values


def _enum_subkeys(hive, subkey: str) -> List[str]:
    keys: List[str] = []
    if winreg is None:
        return keys
    try:
        with winreg.OpenKey(hive, subkey) as k:
            i = 0
            while True:
                try:
                    name = winreg.EnumKey(k, i)
                    keys.append(name)
                    i += 1
                except OSError:
                    break
    except OSError:
        pass
    return keys


def _collect_from_vendor_key(hive, hive_name: str, base_subkey: str) -> List[HoudiniInstall]:
    installs: List[HoudiniInstall] = []
    # Typical subkeys: 'Houdini 20.5.293', 'Houdini 19.5.716', or sometimes a plain 'Houdini'
    for sub in _enum_subkeys(hive, base_subkey):
        if not sub.lower().startswith("houdini"):
            continue
        full_key = base_subkey + "\\" + sub
        vals = _read_key_values(hive, full_key)
        # Try to find an install path value
        install_path = None
        for name in POSSIBLE_VALUE_NAMES:
            if name in vals:
                install_path = vals[name]
                break
        # Some keys place the path under a nested subkey, try common ones
        if not install_path:
            for nested in ("CurrentVersion", "InstallPath", "Settings"):
                nested_key = full_key + "\\" + nested
                nested_vals = _read_key_values(hive, nested_key)
                for name in POSSIBLE_VALUE_NAMES:
                    if name in nested_vals:
                        install_path = nested_vals[name]
                        break
                if install_path:
                    full_key = nested_key
                    vals.update(nested_vals)
                    break
        if not install_path:
            continue
        # Determine version: either from key name, or explicit 'Version' value
        ver = vals.get("Version") or sub.replace("Houdini", "").strip()
        if not ver:
            ver = os.path.basename(str(install_path))  # e.g. 'Houdini 20.5.293'
        sort_key = HoudiniInstall.parse_version(ver)
        installs.append(HoudiniInstall(sort_key=sort_key, version=ver, install_path=install_path, reg_hive=hive_name, reg_key=full_key))
    return installs


def _find_via_registry() -> List[HoudiniInstall]:
    if platform.system().lower() != 'windows' or winreg is None:
        return []
    installs: List[HoudiniInstall] = []
    # Probe 64-bit and 32-bit registry views
    hives = [
        (winreg.HKEY_LOCAL_MACHINE, "HKLM"),
        (winreg.HKEY_CURRENT_USER, "HKCU"),
    ]
    vendor_subkeys = [
        HOUDINI_VENDOR_KEY,  # 64-bit view
        r"Software\WOW6432Node\Side Effects Software",  # 32-bit view on 64-bit OS
    ]
    for hive, hive_name in hives:
        for subkey in vendor_subkeys:
            installs.extend(_collect_from_vendor_key(hive, hive_name, subkey))
    # Deduplicate by install_path
    dedup: Dict[str, HoudiniInstall] = {}
    for i in installs:
        dedup.setdefault(os.path.normpath(i.install_path), i)
    return list(dedup.values())


def _find_via_env() -> Optional[HoudiniInstall]:
    hfs = os.environ.get("HFS")
    if not hfs:
        return None
    ver = os.environ.get("HOUDINI_VERSION", "")
    if not ver:
        # Try to parse from folder name
        base = os.path.basename(os.path.normpath(hfs))
        m = _version_re.search(base)
        ver = m.group(0) if m else ""
    sort_key = HoudiniInstall.parse_version(ver)
    return HoudiniInstall(sort_key=sort_key, version=ver or "", install_path=hfs, reg_hive="env", reg_key="HFS")


def _find_via_default_paths() -> List[HoudiniInstall]:
    # Fallback scan under typical install roots
    roots = [
        r"C:\\Program Files\\Side Effects Software",
        r"C:\\Program Files (x86)\\Side Effects Software",
    ]
    installs: List[HoudiniInstall] = []
    for root in roots:
        if not os.path.isdir(root):
            continue
        try:
            for name in os.listdir(root):
                if not name.lower().startswith("houdini"):
                    continue
                path = os.path.join(root, name)
                if not os.path.isdir(path):
                    continue
                # Expect directory like 'Houdini 20.5.293'
                m = _version_re.search(name)
                ver = m.group(0) if m else ""
                installs.append(HoudiniInstall(sort_key=HoudiniInstall.parse_version(ver), version=ver, install_path=path, reg_hive="fs", reg_key=root))
        except Exception:
            pass
    return installs


def find_installations(include_env: bool = True, include_fs_fallback: bool = True) -> List[Dict[str, str]]:
    """Return a list of dicts {version, install_path} for discovered Houdini installations.
    Searches: Windows Registry (HKLM/HKCU, 64/32 views), then optional HFS env, then filesystem roots.
    Results are sorted by version descending (latest first) when version can be parsed.
    """
    found: List[HoudiniInstall] = _find_via_registry()
    if include_env:
        env_hit = _find_via_env()
        if env_hit:
            found.append(env_hit)
    if include_fs_fallback:
        found.extend(_find_via_default_paths())

    # Dedup by path (normalize)
    dedup: Dict[str, HoudiniInstall] = {}
    for item in found:
        dedup[os.path.normpath(item.install_path)] = item
    unique = list(dedup.values())

    # Sort by version tuple descending; unknown versions go last
    unique.sort(key=lambda x: x.sort_key, reverse=True)
    return [{"version": i.version, "install_path": i.install_path} for i in unique]


def find_latest_installation() -> Optional[Dict[str, str]]:
    """Return the latest Houdini installation dict or None if not found."""
    installs = find_installations()
    return installs[0] if installs else None


# ----------------------------- CLI -----------------------------

def _main(argv: List[str]) -> int:
    import argparse
    parser = argparse.ArgumentParser(description="Locate Houdini installation paths (Windows)")
    g = parser.add_mutually_exclusive_group()
    g.add_argument('--latest', action='store_true', help='Print latest installation')
    g.add_argument('--all', action='store_true', help='Print all installations')
    parser.add_argument('--json', action='store_true', help='Output JSON instead of plain text')
    args = parser.parse_args(argv)

    if args.latest:
        latest = find_latest_installation()
        if args.json:
            print(json.dumps(latest or {}, ensure_ascii=False))
        else:
            print(latest["install_path"] if latest else "")
        return 0

    # default to --all
    all_inst = find_installations()
    if args.json:
        print(json.dumps(all_inst, ensure_ascii=False, indent=2))
    else:
        for item in all_inst:
            print(f"{item['version'] or 'unknown'}\t{item['install_path']}")
    return 0


if __name__ == '__main__':  # pragma: no cover
    sys.exit(_main(sys.argv[1:]))

