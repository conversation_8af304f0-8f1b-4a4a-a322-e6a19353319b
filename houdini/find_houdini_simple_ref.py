# Minimal Ho<PERSON> path finder by version (Windows), small functions (<20 lines each)

import re

def _is_windows():
    import platform
    return platform.system().lower() == 'windows'

def _get_winreg():
    try:
        import winreg
        return winreg
    except Exception:
        return None

def _parse_ver_tuple(s):
    m = re.search(r'(\d+)\.(\d+)(?:\.(\d+))?', s or '')
    if not m:
        return (0, 0, 0)
    return (int(m.group(1)), int(m.group(2)), int(m.group(3) or '0'))

def _enum_subkeys(winreg, hive, base):
    keys = []
    try:
        with winreg.OpenKey(hive, base) as root:
            i = 0
            while True:
                try:
                    keys.append(winreg.EnumKey(root, i)); i += 1
                except OSError:
                    break
    except OSError:
        pass
    return keys

def _read_values(winreg, hive, key):
    vals = {}
    try:
        with winreg.OpenKey(hive, key) as k:
            i = 0
            while True:
                try:
                    name, data, _ = winreg.EnumValue(k, i)
                    vals[name] = str(data); i += 1
                except OSError:
                    break
    except OSError:
        pass
    return vals

def _find_path_in_key(winreg, hive, key, names):
    vals = _read_values(winreg, hive, key)
    for n in names:
        if n in vals:
            return vals[n], vals
    for nested in ('CurrentVersion', 'InstallPath', 'Settings'):
        nkey = key + '\\' + nested
        sub = _read_values(winreg, hive, nkey)
        for n in names:
            if n in sub:
                vals.update(sub)
                return sub[n], vals
    return None, vals

def _scan_base(winreg, hive, base, version, names, results):
    for sub in _enum_subkeys(winreg, hive, base):
        if not sub.lower().startswith('houdini'):
            continue
        key = base + '\\' + sub
        path, vals = _find_path_in_key(winreg, hive, key, names)
        if not path:
            continue
        ver = (vals.get('Version') or sub.replace('Houdini', '').strip() or
               (re.search(r'\d+\.\d+(?:\.\d+)?', path) or [None])[0])
        if (ver or '').strip() == version:
            return path
        results.append((_parse_ver_tuple(ver), path))
    return None

def _select_best(results, version):
    t = _parse_ver_tuple(version)
    branch = [r for r in results if r[0][0] == t[0] and r[0][1] == t[1]]
    if not branch:
        return None
    branch.sort(reverse=True)
    return branch[0][1]

def find_houdini_path(version):
    if not _is_windows():
        return None
    winreg = _get_winreg()
    if not winreg:
        return None
    version = (version or '').strip()
    names = ('InstallPath', 'InstallFolder', 'Path', 'Location')
    bases = [r'Software\\Side Effects Software', r'Software\\WOW6432Node\\Side Effects Software']
    results = []
    for hive in (winreg.HKEY_LOCAL_MACHINE, winreg.HKEY_CURRENT_USER):
        for base in bases:
            hit = _scan_base(winreg, hive, base, version, names, results)
            if hit:
                return hit
    return _select_best(results, version)

