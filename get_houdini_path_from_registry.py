"""
通过注册表获取Houdini安装路径
支持多个版本的Houdini检测
"""

import winreg
import os
from pathlib import Path

def get_houdini_install_path(version=None):
    """
    通过注册表获取Houdini安装路径
    
    Args:
        version (str): 指定版本号，如 "20.5" 或 "20.5.522"，None表示查找所有版本
    
    Returns:
        str or dict: 如果指定版本返回路径字符串，否则返回版本字典
    """
    
    # Houdini在注册表中的可能位置
    registry_paths = [
        # 64位系统上的64位程序
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Side Effects Software"),
        # 64位系统上的32位程序
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Side Effects Software"),
        # 当前用户
        (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Side Effects Software"),
    ]
    
    found_versions = {}
    
    for hkey, base_path in registry_paths:
        try:
            # 打开Side Effects Software键
            with winreg.OpenKey(hkey, base_path) as key:
                # 枚举所有子键（Houdini版本）
                i = 0
                while True:
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        
                        # 检查是否是Houdini相关的键
                        if "Houdini" in subkey_name:
                            try:
                                # 打开Houdini版本键
                                houdini_path = f"{base_path}\\{subkey_name}"
                                with winreg.OpenKey(hkey, houdini_path) as houdini_key:
                                    
                                    # 尝试获取安装路径
                                    install_path = None
                                    try:
                                        install_path, _ = winreg.QueryValueEx(houdini_key, "InstallPath")
                                    except FileNotFoundError:
                                        try:
                                            install_path, _ = winreg.QueryValueEx(houdini_key, "InstallDir")
                                        except FileNotFoundError:
                                            try:
                                                install_path, _ = winreg.QueryValueEx(houdini_key, "")
                                            except FileNotFoundError:
                                                pass
                                    
                                    if install_path and os.path.exists(install_path):
                                        # 提取版本号
                                        version_str = subkey_name.replace("Houdini", "").strip()
                                        if version_str:
                                            found_versions[version_str] = install_path
                                            print(f"找到 Houdini {version_str}: {install_path}")
                                        
                            except Exception as e:
                                continue
                        
                        i += 1
                        
                    except OSError:
                        # 没有更多子键
                        break
                        
        except Exception as e:
            continue
    
    # 如果指定了版本，返回对应路径
    if version:
        # 精确匹配
        if version in found_versions:
            return found_versions[version]
        
        # 模糊匹配（例如输入"20.5"匹配"20.5.522"）
        for ver, path in found_versions.items():
            if ver.startswith(version):
                return path
        
        return None
    
    return found_versions

def get_houdini_20_5_path():
    """
    专门获取Houdini 20.5.522的安装路径
    """
    
    # 尝试多种版本号格式
    version_formats = [
        "20.5.522",
        "20.5",
        " 20.5.522",
        " 20.5",
        "20.5.522 ",
        "20.5 "
    ]
    
    for version_format in version_formats:
        path = get_houdini_install_path(version_format)
        if path:
            return path
    
    # 如果没找到，列出所有版本让用户选择
    all_versions = get_houdini_install_path()
    
    print("未找到Houdini 20.5.522，以下是找到的所有版本:")
    for ver, path in all_versions.items():
        print(f"  {ver}: {path}")
    
    # 尝试找最接近的版本
    for ver, path in all_versions.items():
        if "20.5" in ver:
            print(f"使用最接近的版本: {ver}")
            return path
    
    return None

def get_houdini_executable_path(version=None):
    """
    获取Houdini可执行文件路径
    
    Args:
        version (str): 版本号
    
    Returns:
        dict: 包含各种可执行文件路径的字典
    """
    
    install_path = get_houdini_install_path(version) if version else get_houdini_20_5_path()
    
    if not install_path:
        return None
    
    install_path = Path(install_path)
    bin_path = install_path / "bin"
    
    executables = {}
    
    # 常见的Houdini可执行文件
    exe_files = [
        "houdini.exe",
        "houdinifx.exe", 
        "houdinicore.exe",
        "houdini-bin.exe",
        "hython.exe",
        "hbatch.exe"
    ]
    
    for exe_file in exe_files:
        exe_path = bin_path / exe_file
        if exe_path.exists():
            exe_name = exe_file.replace(".exe", "")
            executables[exe_name] = str(exe_path)
    
    return executables

def verify_houdini_installation(install_path):
    """
    验证Houdini安装是否完整
    
    Args:
        install_path (str): 安装路径
    
    Returns:
        dict: 验证结果
    """
    
    if not install_path or not os.path.exists(install_path):
        return {"valid": False, "reason": "路径不存在"}
    
    install_path = Path(install_path)
    
    # 检查关键目录和文件
    checks = {
        "bin目录": install_path / "bin",
        "houdini.exe": install_path / "bin" / "houdini.exe",
        "python目录": install_path / "python",
        "houdini目录": install_path / "houdini"
    }
    
    results = {}
    all_valid = True
    
    for name, path in checks.items():
        exists = path.exists()
        results[name] = {"path": str(path), "exists": exists}
        if not exists:
            all_valid = False
    
    return {
        "valid": all_valid,
        "install_path": str(install_path),
        "checks": results
    }

def main():
    """
    主函数：演示所有功能
    """
    
    print("="*60)
    print("Houdini安装路径检测")
    print("="*60)
    
    # 1. 查找所有版本
    print("\n1. 查找所有Houdini版本:")
    all_versions = get_houdini_install_path()
    
    if all_versions:
        for version, path in all_versions.items():
            print(f"   Houdini {version}: {path}")
    else:
        print("   未找到任何Houdini安装")
        return
    
    # 2. 查找特定版本 (20.5.522)
    print(f"\n2. 查找Houdini 20.5.522:")
    target_path = get_houdini_20_5_path()
    
    if target_path:
        print(f"   找到: {target_path}")
        
        # 3. 验证安装
        print(f"\n3. 验证安装:")
        verification = verify_houdini_installation(target_path)
        
        if verification["valid"]:
            print("   ✅ 安装完整")
        else:
            print("   ❌ 安装不完整")
        
        for name, info in verification["checks"].items():
            status = "✅" if info["exists"] else "❌"
            print(f"   {status} {name}: {info['path']}")
        
        # 4. 获取可执行文件路径
        print(f"\n4. 可执行文件:")
        executables = get_houdini_executable_path("20.5")
        
        if executables:
            for name, path in executables.items():
                print(f"   {name}: {path}")
        else:
            print("   未找到可执行文件")
            
    else:
        print("   未找到Houdini 20.5.522")
    
    # 5. 使用示例
    print(f"\n5. 使用示例:")
    print("   # 获取特定版本路径")
    print("   path = get_houdini_install_path('20.5.522')")
    print("   ")
    print("   # 获取所有版本")
    print("   versions = get_houdini_install_path()")
    print("   ")
    print("   # 获取可执行文件")
    print("   exes = get_houdini_executable_path('20.5')")

if __name__ == "__main__":
    main()