-- 3ds Max Bounding Box Tool (Multi-Method Version)
-- Author: Assistant
-- Description: Test different bounding box calculation methods

global boundingBoxHelper = undefined
global volumeText = undefined
global bbTool_rollout
global bbToolCallbackActive = false
global currentMethod = 1

-- Function to get bounding box using different methods
function getBoundingBoxByMethod obj method =
(
    if obj == undefined then return undefined
    
    local bbox
    case method of
    (
        1: (
            -- Method 1: World space
            bbox = nodeGetBoundingBox obj (matrix3 1)
        )
        2: (
            -- Method 2: Object transform
            bbox = nodeGetBoundingBox obj obj.transform
        )
        3: (
            -- Method 3: Min/Max properties
            try (
                local objMin = obj.min
                local objMax = obj.max
                bbox = #([objMin.x, objMin.y, objMin.z], [objMax.x, objMax.y, objMax.z])
            ) catch (
                bbox = nodeGetBoundingBox obj (matrix3 1)
            )
        )
        4: (
            -- Method 4: Using object's actual geometry bounds
            try (
                local mesh = obj.mesh
                if mesh != undefined then
                (
                    local meshBBox = meshop.getBoundingBox mesh
                    local tm = obj.objectTransform
                    local minPt = meshBBox[1] * tm
                    local maxPt = meshBBox[2] * tm
                    bbox = #(minPt, maxPt)
                )
                else
                    bbox = nodeGetBoundingBox obj (matrix3 1)
            ) catch (
                bbox = nodeGetBoundingBox obj (matrix3 1)
            )
        )
        default: bbox = nodeGetBoundingBox obj (matrix3 1)
    )
    
    return bbox
)

-- Function to calculate volume
function calculateVolume obj method:1 =
(
    if obj != undefined then
    (
        local bbox = getBoundingBoxByMethod obj method
        if bbox != undefined then
        (
            local dimensions = bbox[2] - bbox[1]
            return dimensions.x * dimensions.y * dimensions.z
        )
    )
    return 0.0
)

-- Function to create bounding box helper
function createBoundingBoxHelper obj method:1 =
(
    if obj != undefined then
    (
        if boundingBoxHelper != undefined then
            delete boundingBoxHelper
        
        local bbox = getBoundingBoxByMethod obj method
        if bbox == undefined then return undefined
        
        local center = (bbox[1] + bbox[2]) / 2
        local dimensions = bbox[2] - bbox[1]
        
        -- Debug output
        local methodNames = #("World Space", "Object Transform", "Min/Max Props", "Mesh Bounds")
        print ("=== Method " + method as string + " (" + methodNames[method] + ") ===")
        print ("BBox Min: " + bbox[1] as string)
        print ("BBox Max: " + bbox[2] as string)
        print ("Center: " + center as string)
        print ("Dimensions: " + dimensions as string)
        print ("Object Pos: " + obj.pos as string)
        
        boundingBoxHelper = Box length:dimensions.x width:dimensions.y height:dimensions.z
        boundingBoxHelper.pos = center
        boundingBoxHelper.wirecolor = color 255 0 0
        boundingBoxHelper.boxmode = true
        boundingBoxHelper.name = "BBox_Method_" + method as string
        boundingBoxHelper.renderable = false
        
        return boundingBoxHelper
    )
    return undefined
)

-- Function to create volume text
function createVolumeText obj method:1 =
(
    if obj != undefined then
    (
        if volumeText != undefined then
            delete volumeText
        
        local bbox = getBoundingBoxByMethod obj method
        if bbox == undefined then return undefined
        
        local volume = calculateVolume obj method:method
        local center = (bbox[1] + bbox[2]) / 2
        local dimensions = bbox[2] - bbox[1]
        
        volumeText = Text()
        local methodNames = #("World", "Transform", "MinMax", "Mesh")
        volumeText.text = methodNames[method] + "\nVol: " + (volume as string) + "\nDim: " + 
                         (dimensions.x as string) + "x" + (dimensions.y as string) + "x" + (dimensions.z as string)
        volumeText.size = 8
        volumeText.pos = [center.x, center.y, bbox[2].z + 20]
        volumeText.wirecolor = color 0 255 0
        volumeText.name = "VolumeText_Method_" + method as string
        
        return volumeText
    )
    return undefined
)

-- Function to update display
function updateBoundingBoxDisplay method:1 =
(
    if selection.count > 0 then
    (
        local obj = selection[1]
        createBoundingBoxHelper obj method:method
        createVolumeText obj method:method
        
        if bbTool_rollout != undefined then
        (
            local volume = calculateVolume obj method:method
            local bbox = getBoundingBoxByMethod obj method
            
            if bbox != undefined then
            (
                local dimensions = bbox[2] - bbox[1]
                bbTool_rollout.lblObjectName.text = "Object: " + obj.name
                bbTool_rollout.lblVolume.text = "Volume: " + (formattedPrint volume format:"f")
                bbTool_rollout.lblDimensions.text = "Dim: " + 
                    (formattedPrint dimensions.x format:"f") + " x " + 
                    (formattedPrint dimensions.y format:"f") + " x " + 
                    (formattedPrint dimensions.z format:"f")
            )
        )
    )
    else
    (
        if boundingBoxHelper != undefined then
        (
            delete boundingBoxHelper
            boundingBoxHelper = undefined
        )
        if volumeText != undefined then
        (
            delete volumeText
            volumeText = undefined
        )
        
        if bbTool_rollout != undefined then
        (
            bbTool_rollout.lblObjectName.text = "Object: None selected"
            bbTool_rollout.lblVolume.text = "Volume: 0.0"
            bbTool_rollout.lblDimensions.text = "Dimensions: 0 x 0 x 0"
        )
    )
)

-- Function to clear display
function clearBoundingBoxDisplay =
(
    if boundingBoxHelper != undefined then
    (
        delete boundingBoxHelper
        boundingBoxHelper = undefined
    )
    if volumeText != undefined then
    (
        delete volumeText
        volumeText = undefined
    )
)

-- Function to register callback
function registerCallback =
(
    if not bbToolCallbackActive then
    (
        callbacks.addScript #selectionSetChanged "if bbTool_rollout != undefined then updateBoundingBoxDisplay method:currentMethod" id:#bbToolCallback
        bbToolCallbackActive = true
        print "Callback registered"
    )
)

-- Function to unregister callback
function unregisterCallback =
(
    if bbToolCallbackActive then
    (
        callbacks.removeScripts id:#bbToolCallback
        bbToolCallbackActive = false
        print "Callback unregistered"
    )
)

-- Rollout interface
rollout bbTool_rollout "BBox Multi-Method" width:300 height:280
(
    label lblTitle "Bounding Box Multi-Method Tool"
    label lblObjectName "Object: None selected"
    label lblVolume "Volume: 0.0"
    label lblDimensions "Dimensions: 0 x 0 x 0"
    
    group "Method"
    (
        radiobuttons rbMethod labels:#("World Space", "Object Transform", "Min/Max", "Mesh Bounds") default:1 columns:2
    )
    
    button btnUpdate "Update" width:90 height:25 across:3
    button btnClear "Clear" width:90 height:25
    button btnClose "Close" width:90 height:25
    
    checkbox chkAutoUpdate "Auto Update" checked:true
    
    on rbMethod changed state do
    (
        currentMethod = state
        updateBoundingBoxDisplay method:currentMethod
    )
    
    on btnUpdate pressed do updateBoundingBoxDisplay method:currentMethod
    on btnClear pressed do clearBoundingBoxDisplay()
    on btnClose pressed do
    (
        clearBoundingBoxDisplay()
        unregisterCallback()
        destroyDialog bbTool_rollout
    )
    
    on bbTool_rollout close do
    (
        clearBoundingBoxDisplay()
        unregisterCallback()
    )
)

-- Start function
function startBoundingBoxTool =
(
    try(destroyDialog bbTool_rollout) catch()
    unregisterCallback()
    createDialog bbTool_rollout
    registerCallback()
    updateBoundingBoxDisplay method:currentMethod
)

-- Start the tool
startBoundingBoxTool()

print "Multi-Method Bounding Box Tool loaded!"
print "Try different methods (1-4) to find the correct bounding box calculation."
print "Check the listener for debug information."
