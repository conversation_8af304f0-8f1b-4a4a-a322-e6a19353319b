# 3ds Max Bounding Box Tool - 更新日志

## 主要修复和改进

### 🔧 BoundingBox位置问题修复

**问题**: 原始代码中bounding box位置不正确，没有正确包裹住模型

**原因**: 使用了 `nodeGetBoundingBox obj obj.transform`，这会基于对象的本地坐标系计算边界框，对于有旋转、缩放等变换的对象会导致位置错误。

**修复**: 改为使用 `nodeGetBoundingBox obj (matrix3 1)`，基于世界坐标系计算，确保边界框正确包裹模型。

```maxscript
-- 修复前 (错误)
local bbox = nodeGetBoundingBox obj obj.transform

-- 修复后 (正确)
local bbox = nodeGetBoundingBox obj (matrix3 1)
```

### 🆕 Callback管理功能

**新增功能**: 
- Register Callback按钮 - 手动启用自动更新
- Unregister Callback按钮 - 手动禁用自动更新
- 状态跟踪变量 `bbToolCallbackActive`

**好处**:
- 避免重复注册callback
- 可以手动控制性能影响
- 工具关闭时自动清理，防止内存泄漏

### 📁 文件版本说明

1. **BoundingBoxTool.ms** - 原始版本（有位置问题）
2. **BoundingBoxTool_Fixed.ms** - 字符编码修复版本（仍有位置问题）
3. **BoundingBoxTool_Fixed_v2.ms** - 完全修复版本（推荐使用）

## 使用建议

### 推荐版本
使用 **BoundingBoxTool_Fixed_v2.ms**，这是最稳定和功能最完整的版本。

### 性能优化
- 对于复杂场景，可以取消勾选"Auto Update on Selection"
- 或者使用"Unregister Callback"按钮临时禁用自动更新
- 需要时手动点击"Update Display"

### 调试信息
工具会在MAXScript监听器中输出详细信息：
- 对象名称
- 体积计算结果
- 尺寸信息
- Callback状态变化

## 技术细节

### 坐标系统
- 使用世界坐标系 `(matrix3 1)` 确保准确性
- 适用于任何变换状态的对象

### 内存管理
- 自动清理创建的辅助对象
- 工具关闭时注销所有callback
- 避免场景污染

### 错误处理
- 使用 `try/catch` 处理对话框创建
- 检查对象有效性
- 安全的callback管理

## 故障排除

### 如果BoundingBox仍然位置不正确：
1. 确保使用 `BoundingBoxTool_Fixed_v2.ms`
2. 检查对象是否有复杂的层级结构
3. 尝试重置对象的变换

### 如果自动更新不工作：
1. 点击"Register Callback"按钮
2. 确保勾选了"Auto Update on Selection"
3. 检查MAXScript监听器的错误信息

### 性能问题：
1. 点击"Unregister Callback"禁用自动更新
2. 手动使用"Update Display"按钮
3. 清理不需要的显示元素

---

**版本**: 2.0  
**更新日期**: 2025-07-07  
**兼容性**: 3ds Max 2018+
