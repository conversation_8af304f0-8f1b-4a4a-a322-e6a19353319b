"""
图片转Base64编码工具 - GUI版本
使用PySide6创建图形界面
支持拖拽、批量处理、预览等功能
"""

import sys
import base64
import json
from pathlib import Path
from typing import Optional
import mimetypes

try:
    from PySide2.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                                   QHBoxLayout, QLabel, QPushButton, QTextEdit, 
                                   QFileDialog, QCheckBox, QProgressBar, QTabWidget,
                                   QListWidget, QSplitter, QGroupBox, QMessageBox)
    from PySide2.QtCore import Qt, QThread, Signal, QMimeData
    from PySide2.QtGui import QFont, QPixmap, QDragEnterEvent, QDropEvent
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available. Please install it: pip install PySide6")

class ImageProcessor(QThread):
    """图片处理线程"""
    progress_updated = Signal(int)
    file_processed = Signal(str, str)  # filename, base64_string
    finished_processing = Signal()
    
    def __init__(self, file_paths, include_data_url=True):
        super().__init__()
        self.file_paths = file_paths
        self.include_data_url = include_data_url
    
    def run(self):
        total_files = len(self.file_paths)
        
        for i, file_path in enumerate(self.file_paths):
            try:
                base64_string = self.image_to_base64(file_path, self.include_data_url)
                if base64_string:
                    self.file_processed.emit(Path(file_path).name, base64_string)
                
                # 更新进度
                progress = int((i + 1) / total_files * 100)
                self.progress_updated.emit(progress)
                
            except Exception as e:
                print(f"处理文件失败 {file_path}: {e}")
        
        self.finished_processing.emit()
    
    def image_to_base64(self, image_path: str, include_data_url: bool = True) -> Optional[str]:
        """将图片转换为Base64编码"""
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
            
            base64_string = base64.b64encode(image_data).decode('utf-8')
            
            if include_data_url:
                mime_type, _ = mimetypes.guess_type(image_path)
                if mime_type is None:
                    ext = Path(image_path).suffix.lower()
                    mime_map = {
                        '.png': 'image/png', '.jpg': 'image/jpeg', '.jpeg': 'image/jpeg',
                        '.gif': 'image/gif', '.bmp': 'image/bmp', '.webp': 'image/webp',
                        '.svg': 'image/svg+xml', '.ico': 'image/x-icon'
                    }
                    mime_type = mime_map.get(ext, 'image/png')
                
                base64_string = f"data:{mime_type};base64,{base64_string}"
            
            return base64_string
            
        except Exception as e:
            print(f"转换失败: {e}")
            return None

class DropArea(QWidget):
    """支持拖拽的区域"""
    files_dropped = Signal(list)
    
    def __init__(self):
        super().__init__()
        self.setAcceptDrops(True)
        self.setMinimumHeight(100)
        self.setStyleSheet("""
            QWidget {
                border: 2px dashed #aaa;
                border-radius: 10px;
                background-color: #f9f9f9;
            }
            QWidget:hover {
                border-color: #0078d4;
                background-color: #e6f3ff;
            }
        """)
        
        layout = QVBoxLayout()
        label = QLabel("拖拽图片文件到这里\n或点击下方按钮选择文件")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("border: none; color: #666; font-size: 14px;")
        layout.addWidget(label)
        self.setLayout(layout)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
    
    def dropEvent(self, event: QDropEvent):
        files = []
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            if Path(file_path).is_file():
                files.append(file_path)
        
        if files:
            self.files_dropped.emit(files)

class ImageToBase64GUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.processor_thread = None
        self.results = {}
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("图片转Base64编码工具")
        self.setGeometry(100, 100, 900, 700)
        
        # 中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("图片转Base64编码工具")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 拖拽区域
        self.drop_area = DropArea()
        self.drop_area.files_dropped.connect(self.handle_dropped_files)
        main_layout.addWidget(self.drop_area)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.select_files_btn = QPushButton("选择文件")
        self.select_files_btn.clicked.connect(self.select_files)
        button_layout.addWidget(self.select_files_btn)
        
        self.select_folder_btn = QPushButton("选择文件夹")
        self.select_folder_btn.clicked.connect(self.select_folder)
        button_layout.addWidget(self.select_folder_btn)
        
        self.clear_btn = QPushButton("清空结果")
        self.clear_btn.clicked.connect(self.clear_results)
        button_layout.addWidget(self.clear_btn)
        
        button_layout.addStretch()
        
        # 选项
        self.include_data_url_cb = QCheckBox("包含Data URL前缀")
        self.include_data_url_cb.setChecked(True)
        button_layout.addWidget(self.include_data_url_cb)
        
        main_layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：文件列表
        left_group = QGroupBox("处理的文件")
        left_layout = QVBoxLayout(left_group)
        
        self.file_list = QListWidget()
        left_layout.addWidget(self.file_list)
        
        splitter.addWidget(left_group)
        
        # 右侧：Base64结果
        right_group = QGroupBox("Base64结果")
        right_layout = QVBoxLayout(right_group)
        
        self.result_text = QTextEdit()
        self.result_text.setFont(QFont("Consolas", 9))
        right_layout.addWidget(self.result_text)
        
        # 结果操作按钮
        result_button_layout = QHBoxLayout()
        
        self.copy_btn = QPushButton("复制到剪贴板")
        self.copy_btn.clicked.connect(self.copy_to_clipboard)
        result_button_layout.addWidget(self.copy_btn)
        
        self.save_btn = QPushButton("保存到文件")
        self.save_btn.clicked.connect(self.save_to_file)
        result_button_layout.addWidget(self.save_btn)
        
        result_button_layout.addStretch()
        right_layout.addLayout(result_button_layout)
        
        splitter.addWidget(right_group)
        
        # 设置分割器比例
        splitter.setSizes([300, 600])
        
        # 文件列表选择事件
        self.file_list.currentItemChanged.connect(self.on_file_selected)
    
    def select_files(self):
        """选择文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择图片文件", "",
            "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp *.webp *.svg *.ico);;所有文件 (*)"
        )
        
        if file_paths:
            self.process_files(file_paths)
    
    def select_folder(self):
        """选择文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择包含图片的文件夹")
        
        if folder_path:
            # 获取文件夹中的所有图片文件
            image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.svg', '.ico'}
            file_paths = []
            
            for file_path in Path(folder_path).iterdir():
                if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                    file_paths.append(str(file_path))
            
            if file_paths:
                self.process_files(file_paths)
            else:
                QMessageBox.information(self, "提示", "所选文件夹中没有找到图片文件")
    
    def handle_dropped_files(self, file_paths):
        """处理拖拽的文件"""
        # 过滤图片文件
        image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.svg', '.ico'}
        image_files = [f for f in file_paths if Path(f).suffix.lower() in image_extensions]
        
        if image_files:
            self.process_files(image_files)
        else:
            QMessageBox.warning(self, "警告", "没有找到支持的图片文件")
    
    def process_files(self, file_paths):
        """处理文件"""
        if self.processor_thread and self.processor_thread.isRunning():
            return
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 创建处理线程
        include_data_url = self.include_data_url_cb.isChecked()
        self.processor_thread = ImageProcessor(file_paths, include_data_url)
        
        # 连接信号
        self.processor_thread.progress_updated.connect(self.progress_bar.setValue)
        self.processor_thread.file_processed.connect(self.add_result)
        self.processor_thread.finished_processing.connect(self.processing_finished)
        
        # 启动线程
        self.processor_thread.start()
    
    def add_result(self, filename, base64_string):
        """添加处理结果"""
        self.results[filename] = base64_string
        self.file_list.addItem(filename)
    
    def processing_finished(self):
        """处理完成"""
        self.progress_bar.setVisible(False)
        QMessageBox.information(self, "完成", f"成功处理了 {len(self.results)} 个文件")
    
    def on_file_selected(self, current, previous):
        """文件选择事件"""
        if current:
            filename = current.text()
            if filename in self.results:
                self.result_text.setPlainText(self.results[filename])
    
    def copy_to_clipboard(self):
        """复制到剪贴板"""
        text = self.result_text.toPlainText()
        if text:
            QApplication.clipboard().setText(text)
            QMessageBox.information(self, "成功", "已复制到剪贴板")
    
    def save_to_file(self):
        """保存到文件"""
        if not self.results:
            QMessageBox.warning(self, "警告", "没有结果可保存")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存结果", "base64_results.json",
            "JSON文件 (*.json);;文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                if file_path.endswith('.json'):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(self.results, f, indent=2, ensure_ascii=False)
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        for filename, base64_string in self.results.items():
                            f.write(f"=== {filename} ===\n")
                            f.write(f"{base64_string}\n\n")
                
                QMessageBox.information(self, "成功", f"结果已保存到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败: {e}")
    
    def clear_results(self):
        """清空结果"""
        self.results.clear()
        self.file_list.clear()
        self.result_text.clear()

def main():
    if not PYSIDE6_AVAILABLE:
        print("请安装PySide6: pip install PySide6")
        return
    
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    window = ImageToBase64GUI()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
