-- 3ds Max Bounding Box and Volume Display Tool
-- Author: Assistant
-- Description: Displays bounding box and volume information for selected objects

-- Global variables
global boundingBoxHelper = undefined
global volumeText = undefined
global bbTool_rollout

-- Function to calculate volume from bounding box
function calculateVolume obj =
(
    if obj != undefined then
    (
        local bbox = nodeGetBoundingBox obj obj.transform
        local dimensions = bbox[2] - bbox[1]
        local volume = dimensions.x * dimensions.y * dimensions.z
        return volume
    )
    else
        return 0.0
)

-- Function to create bounding box visualization
function createBoundingBoxHelper obj =
(
    if obj != undefined then
    (
        -- Delete existing helper if it exists
        if boundingBoxHelper != undefined then
            delete boundingBoxHelper
        
        -- Get bounding box
        local bbox = nodeGetBoundingBox obj obj.transform
        local center = (bbox[1] + bbox[2]) / 2
        local dimensions = bbox[2] - bbox[1]
        
        -- Create box helper
        boundingBoxHelper = Box length:dimensions.x width:dimensions.y height:dimensions.z
        boundingBoxHelper.pos = center
        boundingBoxHelper.wirecolor = color 255 0 0  -- Red color
        boundingBoxHelper.boxmode = true
        boundingBoxHelper.name = "BoundingBox_Helper"
        
        -- Make it non-renderable and see-through
        boundingBoxHelper.renderable = false
        boundingBoxHelper.xray = true
        
        return boundingBoxHelper
    )
    return undefined
)

-- Function to create volume text display
function createVolumeText obj =
(
    if obj != undefined then
    (
        -- Delete existing text if it exists
        if volumeText != undefined then
            delete volumeText
        
        local volume = calculateVolume obj
        local bbox = nodeGetBoundingBox obj obj.transform
        local center = (bbox[1] + bbox[2]) / 2
        local dimensions = bbox[2] - bbox[1]
        
        -- Create text object
        volumeText = Text()
        volumeText.text = "Volume: " + (volume as string) + " units³\n" + 
                         "Dimensions: " + (dimensions.x as string) + " x " + 
                         (dimensions.y as string) + " x " + (dimensions.z as string)
        volumeText.size = 10
        volumeText.pos = [center.x, center.y, bbox[2].z + 20]  -- Position above object
        volumeText.wirecolor = color 0 255 0  -- Green color
        volumeText.name = "Volume_Text"
        
        return volumeText
    )
    return undefined
)

-- Function to update display for current selection
function updateBoundingBoxDisplay =
(
    if selection.count > 0 then
    (
        local obj = selection[1]  -- Use first selected object
        createBoundingBoxHelper obj
        createVolumeText obj
        
        -- Update rollout if it exists
        if bbTool_rollout != undefined then
        (
            local volume = calculateVolume obj
            local bbox = nodeGetBoundingBox obj obj.transform
            local dimensions = bbox[2] - bbox[1]
            
            bbTool_rollout.lblObjectName.text = "Object: " + obj.name
            bbTool_rollout.lblVolume.text = "Volume: " + (formattedPrint volume format:"f") + " units³"
            bbTool_rollout.lblDimensions.text = "Dimensions: " + 
                (formattedPrint dimensions.x format:"f") + " x " + 
                (formattedPrint dimensions.y format:"f") + " x " + 
                (formattedPrint dimensions.z format:"f")
        )
    )
    else
    (
        -- Clear display if no selection
        if boundingBoxHelper != undefined then
        (
            delete boundingBoxHelper
            boundingBoxHelper = undefined
        )
        if volumeText != undefined then
        (
            delete volumeText
            volumeText = undefined
        )
        
        -- Update rollout
        if bbTool_rollout != undefined then
        (
            bbTool_rollout.lblObjectName.text = "Object: None selected"
            bbTool_rollout.lblVolume.text = "Volume: 0.0 units³"
            bbTool_rollout.lblDimensions.text = "Dimensions: 0 x 0 x 0"
        )
    )
)

-- Function to clear all helpers
function clearBoundingBoxDisplay =
(
    if boundingBoxHelper != undefined then
    (
        delete boundingBoxHelper
        boundingBoxHelper = undefined
    )
    if volumeText != undefined then
    (
        delete volumeText
        volumeText = undefined
    )
)

-- Rollout for the tool interface
rollout bbTool_rollout "Bounding Box Tool" width:300 height:200
(
    label lblTitle "Bounding Box & Volume Tool" style_sunkenedge:true
    separator sep1 width:280 height:2
    
    label lblObjectName "Object: None selected" align:#left
    label lblVolume "Volume: 0.0 units³" align:#left
    label lblDimensions "Dimensions: 0 x 0 x 0" align:#left
    
    separator sep2 width:280 height:2
    
    button btnUpdate "Update Display" width:120 height:30 align:#left across:2
    button btnClear "Clear Display" width:120 height:30 align:#right
    
    checkbox chkAutoUpdate "Auto Update on Selection" checked:true
    
    separator sep3 width:280 height:2
    
    button btnClose "Close Tool" width:280 height:25
    
    -- Event handlers
    on btnUpdate pressed do
    (
        updateBoundingBoxDisplay()
    )
    
    on btnClear pressed do
    (
        clearBoundingBoxDisplay()
        lblObjectName.text = "Object: None selected"
        lblVolume.text = "Volume: 0.0 units³"
        lblDimensions.text = "Dimensions: 0 x 0 x 0"
    )
    
    on btnClose pressed do
    (
        clearBoundingBoxDisplay()
        destroyDialog bbTool_rollout
    )
    
    on bbTool_rollout close do
    (
        clearBoundingBoxDisplay()
    )
)

-- Selection change callback
callbacks.removeScripts id:#bbToolCallback
callbacks.addScript #selectionSetChanged "if bbTool_rollout != undefined and bbTool_rollout.chkAutoUpdate.checked then updateBoundingBoxDisplay()" id:#bbToolCallback

-- Function to start the tool
function startBoundingBoxTool =
(
    -- Close existing dialog if open
    try(destroyDialog bbTool_rollout) catch()
    
    -- Create and show dialog
    createDialog bbTool_rollout
    
    -- Update display for current selection
    updateBoundingBoxDisplay()
)

-- Start the tool
startBoundingBoxTool()

print "Bounding Box Tool loaded successfully!"
print "Select an object and the tool will display its bounding box and volume information."
