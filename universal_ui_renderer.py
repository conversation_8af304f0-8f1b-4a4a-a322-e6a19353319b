"""
通用UI渲染器 - 支持独立Python和DCC软件
适用于Maya、3ds <PERSON>、<PERSON><PERSON><PERSON>、Substance Painter等
"""

import sys
import os
from PySide2.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
from PySide2.QtCore import Qt

class UniversalUIRenderer:
    """通用UI渲染器类"""
    
    @staticmethod
    def get_application():
        """获取或创建QApplication实例"""
        app = QApplication.instance()
        
        if app is None:
            # 独立Python环境，需要创建QApplication
            print("创建新的QApplication实例（独立Python环境）")
            app = QApplication(sys.argv)
            return app, True  # 返回app和是否需要exec_()
        else:
            # DCC软件环境，已有QApplication
            print(f"使用现有QApplication实例（DCC软件环境）")
            return app, False  # 返回app和不需要exec_()
    
    @staticmethod
    def detect_environment():
        """检测当前运行环境"""
        try:
            # 检测Maya
            import maya.cmds
            return "maya"
        except ImportError:
            pass
        
        try:
            # 检测3ds Max
            import pymxs
            return "3dsmax"
        except ImportError:
            pass
        
        try:
            # 检测Houdini
            import hou
            return "houdini"
        except ImportError:
            pass
        
        try:
            # 检测Substance Painter
            import substance_painter
            return "substance_painter"
        except ImportError:
            pass
        
        try:
            # 检测Blender
            import bpy
            return "blender"
        except ImportError:
            pass
        
        # 独立Python环境
        return "standalone"
    
    @staticmethod
    def get_main_window():
        """获取DCC软件的主窗口"""
        environment = UniversalUIRenderer.detect_environment()
        
        if environment == "maya":
            try:
                import maya.OpenMayaUI as omui
                from shiboken2 import wrapInstance
                main_window_ptr = omui.MQtUtil.mainWindow()
                return wrapInstance(int(main_window_ptr), QWidget)
            except:
                return None
        
        elif environment == "3dsmax":
            try:
                import pymxs
                from PySide2.QtWidgets import QWidget
                # 3ds Max的主窗口获取方式
                return QApplication.activeWindow()
            except:
                return None
        
        elif environment == "houdini":
            try:
                import hou
                return hou.qt.mainWindow()
            except:
                return None
        
        elif environment == "substance_painter":
            try:
                import substance_painter.ui
                return substance_painter.ui.get_main_window()
            except:
                return None
        
        # 其他情况返回None
        return None

def render_ui(widget_class, *args, **kwargs):
    """
    通用UI渲染函数
    
    Args:
        widget_class: Widget类（不是实例）
        *args, **kwargs: 传递给widget_class的参数
    
    Returns:
        widget实例
    """
    
    # 获取应用程序实例
    app, needs_exec = UniversalUIRenderer.get_application()
    
    # 检测环境
    environment = UniversalUIRenderer.detect_environment()
    print(f"检测到环境: {environment}")
    
    # 创建widget实例
    widget = widget_class(*args, **kwargs)
    
    # 设置父窗口（如果在DCC软件中）
    if environment != "standalone":
        main_window = UniversalUIRenderer.get_main_window()
        if main_window:
            widget.setParent(main_window)
            widget.setWindowFlags(Qt.Window)
    
    # 显示widget
    widget.show()
    
    # 如果是独立Python环境，需要启动事件循环
    if needs_exec:
        print("启动Qt事件循环...")
        sys.exit(app.exec_())
    
    return widget

# 示例Widget类
class TestWidget(QWidget):
    """测试用的Widget"""
    
    def __init__(self, title="Test Widget"):
        super().__init__()
        self.setWindowTitle(title)
        self.setFixedSize(300, 200)
        
        # 布局
        layout = QVBoxLayout(self)
        
        # 环境信息
        env = UniversalUIRenderer.detect_environment()
        env_label = QLabel(f"当前环境: {env}")
        layout.addWidget(env_label)
        
        # 应用信息
        app = QApplication.instance()
        app_label = QLabel(f"QApplication: {'存在' if app else '不存在'}")
        layout.addWidget(app_label)
        
        # 测试按钮
        test_btn = QPushButton("测试按钮")
        test_btn.clicked.connect(self.on_test_clicked)
        layout.addWidget(test_btn)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
    
    def on_test_clicked(self):
        """测试按钮点击事件"""
        print("测试按钮被点击！")
        env = UniversalUIRenderer.detect_environment()
        print(f"当前运行在: {env}")

# 使用示例
def main():
    """主函数 - 演示如何使用"""
    
    # 方法1: 直接使用render_ui函数
    widget = render_ui(TestWidget, "通用UI测试")
    
    # 方法2: 也可以这样使用
    # widget = render_ui(TestWidget, title="自定义标题")
    
    return widget

# 全局变量存储widget实例（避免被垃圾回收）
_global_widgets = []

def show_test_ui():
    """显示测试UI的便捷函数"""
    global _global_widgets
    
    # 创建并显示UI
    widget = render_ui(TestWidget, "Pipeline工具测试")
    
    # 保存引用
    _global_widgets.append(widget)
    
    return widget

if __name__ == "__main__":
    # 独立运行时
    main()
else:
    # 在DCC软件中导入时，提供便捷函数
    print("通用UI渲染器已加载，使用 show_test_ui() 来测试")