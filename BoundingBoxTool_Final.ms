-- 3ds Max Bounding Box and Volume Display Tool (Final Fixed Version)
-- Author: Assistant
-- Description: Displays bounding box and volume information for selected objects

-- Global variables
global boundingBoxHelper = undefined
global volumeText = undefined
global bbTool_rollout
global bbToolCallbackActive = false

-- Function to calculate volume from bounding box
function calculateVolume obj =
(
    if obj != undefined then
    (
        -- Get bounding box in world space
        local bbox = nodeGetBoundingBox obj (matrix3 1)
        local dimensions = bbox[2] - bbox[1]
        local volume = dimensions.x * dimensions.y * dimensions.z
        return volume
    )
    else
        return 0.0
)

-- Function to create bounding box visualization
function createBoundingBoxHelper obj =
(
    if obj != undefined then
    (
        -- Delete existing helper if it exists
        if boundingBoxHelper != undefined then
            delete boundingBoxHelper
        
        -- Get bounding box in world space (this fixes the positioning issue)
        local bbox = nodeGetBoundingBox obj (matrix3 1)
        local center = (bbox[1] + bbox[2]) / 2
        local dimensions = bbox[2] - bbox[1]
        
        -- Create box helper
        boundingBoxHelper = Box length:dimensions.x width:dimensions.y height:dimensions.z
        boundingBoxHelper.pos = center
        boundingBoxHelper.wirecolor = color 255 0 0  -- Red color
        boundingBoxHelper.boxmode = true
        boundingBoxHelper.name = "BoundingBox_Helper"
        boundingBoxHelper.renderable = false
        
        return boundingBoxHelper
    )
    return undefined
)

-- Function to create volume text display
function createVolumeText obj =
(
    if obj != undefined then
    (
        -- Delete existing text if it exists
        if volumeText != undefined then
            delete volumeText
        
        local volume = calculateVolume obj
        local bbox = nodeGetBoundingBox obj (matrix3 1)
        local center = (bbox[1] + bbox[2]) / 2
        local dimensions = bbox[2] - bbox[1]
        
        -- Create text object
        volumeText = Text()
        local volumeStr = volume as string
        local dimXStr = dimensions.x as string
        local dimYStr = dimensions.y as string
        local dimZStr = dimensions.z as string
        
        volumeText.text = "Volume: " + volumeStr + " units^3\nDimensions: " + dimXStr + " x " + dimYStr + " x " + dimZStr
        volumeText.size = 10
        volumeText.pos = [center.x, center.y, bbox[2].z + 20]
        volumeText.wirecolor = color 0 255 0  -- Green color
        volumeText.name = "Volume_Text"
        
        return volumeText
    )
    return undefined
)

-- Function to update display for current selection
function updateBoundingBoxDisplay =
(
    if selection.count > 0 then
    (
        local obj = selection[1]  -- Use first selected object
        createBoundingBoxHelper obj
        createVolumeText obj
        
        -- Update rollout if it exists
        if bbTool_rollout != undefined then
        (
            local volume = calculateVolume obj
            local bbox = nodeGetBoundingBox obj (matrix3 1)
            local dimensions = bbox[2] - bbox[1]
            
            bbTool_rollout.lblObjectName.text = "Object: " + obj.name
            bbTool_rollout.lblVolume.text = "Volume: " + (formattedPrint volume format:"f") + " units^3"
            bbTool_rollout.lblDimensions.text = "Dimensions: " + 
                (formattedPrint dimensions.x format:"f") + " x " + 
                (formattedPrint dimensions.y format:"f") + " x " + 
                (formattedPrint dimensions.z format:"f")
        )
        
        -- Print to listener for debugging
        print ("Object: " + obj.name)
        print ("Volume: " + (volume as string) + " units^3")
        print ("Dimensions: " + (dimensions.x as string) + " x " + (dimensions.y as string) + " x " + (dimensions.z as string))
    )
    else
    (
        -- Clear display if no selection
        if boundingBoxHelper != undefined then
        (
            delete boundingBoxHelper
            boundingBoxHelper = undefined
        )
        if volumeText != undefined then
        (
            delete volumeText
            volumeText = undefined
        )
        
        -- Update rollout
        if bbTool_rollout != undefined then
        (
            bbTool_rollout.lblObjectName.text = "Object: None selected"
            bbTool_rollout.lblVolume.text = "Volume: 0.0 units^3"
            bbTool_rollout.lblDimensions.text = "Dimensions: 0 x 0 x 0"
        )
        
        print "No object selected"
    )
)

-- Function to clear all helpers
function clearBoundingBoxDisplay =
(
    if boundingBoxHelper != undefined then
    (
        delete boundingBoxHelper
        boundingBoxHelper = undefined
    )
    if volumeText != undefined then
    (
        delete volumeText
        volumeText = undefined
    )
    print "Display cleared"
)

-- Function to register callback
function registerCallback =
(
    if not bbToolCallbackActive then
    (
        callbacks.addScript #selectionSetChanged "if bbTool_rollout != undefined and bbTool_rollout.chkAutoUpdate.checked then updateBoundingBoxDisplay()" id:#bbToolCallback
        bbToolCallbackActive = true
        print "Callback registered"
    )
    else
    (
        print "Callback already active"
    )
)

-- Function to unregister callback
function unregisterCallback =
(
    if bbToolCallbackActive then
    (
        callbacks.removeScripts id:#bbToolCallback
        bbToolCallbackActive = false
        print "Callback unregistered"
    )
    else
    (
        print "Callback already inactive"
    )
)
