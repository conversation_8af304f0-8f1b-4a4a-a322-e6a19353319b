-- 3ds Max Bounding Box and Volume Display Tool (Fixed Version)
-- Author: Assistant
-- Description: Displays bounding box and volume information for selected objects

-- Global variables
global boundingBoxHelper = undefined
global volumeText = undefined
global bbTool_rollout
global bbToolCallbackActive = false

-- Function to calculate volume from bounding box
function calculateVolume obj =
(
    if obj != undefined then
    (
        -- Get bounding box in world space
        local bbox = nodeGetBoundingBox obj (matrix3 1)
        local dimensions = bbox[2] - bbox[1]
        local volume = dimensions.x * dimensions.y * dimensions.z
        return volume
    )
    else
        return 0.0
)

-- Function to create bounding box visualization
function createBoundingBoxHelper obj =
(
    if obj != undefined then
    (
        -- Delete existing helper if it exists
        if boundingBoxHelper != undefined then
            delete boundingBoxHelper
        
        -- Get bounding box in world space (this fixes the positioning issue)
        local bbox = nodeGetBoundingBox obj (matrix3 1)
        local center = (bbox[1] + bbox[2]) / 2
        local dimensions = bbox[2] - bbox[1]
        
        -- Create box helper
        boundingBoxHelper = Box length:dimensions.x width:dimensions.y height:dimensions.z
        boundingBoxHelper.pos = center
        boundingBoxHelper.wirecolor = color 255 0 0  -- Red color
        boundingBoxHelper.boxmode = true
        boundingBoxHelper.name = "BoundingBox_Helper"
        boundingBoxHelper.renderable = false
        
        return boundingBoxHelper
    )
    return undefined
)

-- Function to create volume text display
function createVolumeText obj =
(
    if obj != undefined then
    (
        -- Delete existing text if it exists
        if volumeText != undefined then
            delete volumeText
        
        local volume = calculateVolume obj
        local bbox = nodeGetBoundingBox obj (matrix3 1)
        local center = (bbox[1] + bbox[2]) / 2
        local dimensions = bbox[2] - bbox[1]
        
        -- Create text object
        volumeText = Text()
        local volumeStr = volume as string
        local dimXStr = dimensions.x as string
        local dimYStr = dimensions.y as string
        local dimZStr = dimensions.z as string
        
        volumeText.text = "Volume: " + volumeStr + " units^3\nDimensions: " + dimXStr + " x " + dimYStr + " x " + dimZStr
        volumeText.size = 10
        volumeText.pos = [center.x, center.y, bbox[2].z + 20]
        volumeText.wirecolor = color 0 255 0  -- Green color
        volumeText.name = "Volume_Text"
        
        return volumeText
    )
    return undefined
)

-- Function to update display for current selection
function updateBoundingBoxDisplay =
(
    if selection.count > 0 then
    (
        local obj = selection[1]  -- Use first selected object
        createBoundingBoxHelper obj
        createVolumeText obj
        
        -- Update rollout if it exists
        if bbTool_rollout != undefined then
        (
            local volume = calculateVolume obj
            local bbox = nodeGetBoundingBox obj (matrix3 1)
            local dimensions = bbox[2] - bbox[1]
            
            bbTool_rollout.lblObjectName.text = "Object: " + obj.name
            bbTool_rollout.lblVolume.text = "Volume: " + (formattedPrint volume format:"f") + " units^3"
            bbTool_rollout.lblDimensions.text = "Dimensions: " + 
                (formattedPrint dimensions.x format:"f") + " x " + 
                (formattedPrint dimensions.y format:"f") + " x " + 
                (formattedPrint dimensions.z format:"f")
        )
        
        -- Print to listener for debugging
        print ("Object: " + obj.name)
        print ("Volume: " + (volume as string) + " units^3")
        print ("Dimensions: " + (dimensions.x as string) + " x " + (dimensions.y as string) + " x " + (dimensions.z as string))
    )
    else
    (
        -- Clear display if no selection
        if boundingBoxHelper != undefined then
        (
            delete boundingBoxHelper
            boundingBoxHelper = undefined
        )
        if volumeText != undefined then
        (
            delete volumeText
            volumeText = undefined
        )
        
        -- Update rollout
        if bbTool_rollout != undefined then
        (
            bbTool_rollout.lblObjectName.text = "Object: None selected"
            bbTool_rollout.lblVolume.text = "Volume: 0.0 units^3"
            bbTool_rollout.lblDimensions.text = "Dimensions: 0 x 0 x 0"
        )
        
        print "No object selected"
    )
)

-- Function to clear all helpers
function clearBoundingBoxDisplay =
(
    if boundingBoxHelper != undefined then
    (
        delete boundingBoxHelper
        boundingBoxHelper = undefined
    )
    if volumeText != undefined then
    (
        delete volumeText
        volumeText = undefined
    )
    print "Display cleared"
)

-- Function to register callback
function registerCallback =
(
    if not bbToolCallbackActive then
    (
        callbacks.addScript #selectionSetChanged "if bbTool_rollout != undefined and bbTool_rollout.chkAutoUpdate.checked then updateBoundingBoxDisplay()" id:#bbToolCallback
        bbToolCallbackActive = true
        print "Callback registered"
    )
    else
    (
        print "Callback already active"
    )
)

-- Function to unregister callback
function unregisterCallback =
(
    if bbToolCallbackActive then
    (
        callbacks.removeScripts id:#bbToolCallback
        bbToolCallbackActive = false
        print "Callback unregistered"
    )
    else
    (
        print "Callback already inactive"
    )
)

-- Rollout for the tool interface
rollout bbTool_rollout "Bounding Box Tool" width:300 height:250
(
    label lblTitle "Bounding Box & Volume Tool" style_sunkenedge:true
    separator sep1 width:280 height:2
    
    label lblObjectName "Object: None selected" align:#left
    label lblVolume "Volume: 0.0 units^3" align:#left
    label lblDimensions "Dimensions: 0 x 0 x 0" align:#left
    
    separator sep2 width:280 height:2
    
    button btnUpdate "Update Display" width:120 height:30 align:#left across:2
    button btnClear "Clear Display" width:120 height:30 align:#right
    
    checkbox chkAutoUpdate "Auto Update on Selection" checked:true
    
    separator sep3 width:280 height:2
    
    button btnRegisterCallback "Register Callback" width:130 height:25 align:#left across:2
    button btnUnregisterCallback "Unregister Callback" width:130 height:25 align:#right
    
    separator sep4 width:280 height:2
    
    button btnClose "Close Tool" width:280 height:25
    
    -- Event handlers
    on btnUpdate pressed do
    (
        updateBoundingBoxDisplay()
    )
    
    on btnClear pressed do
    (
        clearBoundingBoxDisplay()
        lblObjectName.text = "Object: None selected"
        lblVolume.text = "Volume: 0.0 units^3"
        lblDimensions.text = "Dimensions: 0 x 0 x 0"
    )
    
    on btnRegisterCallback pressed do
    (
        registerCallback()
    )
    
    on btnUnregisterCallback pressed do
    (
        unregisterCallback()
    )
    
    on btnClose pressed do
    (
        clearBoundingBoxDisplay()
        unregisterCallback()
        destroyDialog bbTool_rollout
    )
    
    on bbTool_rollout close do
    (
        clearBoundingBoxDisplay()
        unregisterCallback()
    )
)

-- Function to start the tool
function startBoundingBoxTool =
(
    -- Close existing dialog if open
    try(destroyDialog bbTool_rollout) catch()
    
    -- Remove any existing callbacks first
    unregisterCallback()
    
    -- Create and show dialog
    createDialog bbTool_rollout
    
    -- Register callback
    registerCallback()
    
    -- Update display for current selection
    updateBoundingBoxDisplay()
)

-- Start the tool
startBoundingBoxTool()

print "Bounding Box Tool loaded successfully!"
print "Select an object and the tool will display its bounding box and volume information."
print "Use 'Register/Unregister Callback' buttons to control automatic updates."
