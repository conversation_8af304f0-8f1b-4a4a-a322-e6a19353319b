"""
简化的Pipeline Shelf加载器
在Houdini中直接运行此脚本
"""

import hou
import json
import os

# 配置文件路径 - 修改为你的JSON文件路径
JSON_CONFIG_PATH = "C:/path/to/your/pipeline_tools.json"

def quick_load_pipeline_shelf():
    """快速加载Pipeline工具架"""
    
    if not os.path.exists(JSON_CONFIG_PATH):
        hou.ui.displayMessage(f"配置文件不存在: {JSON_CONFIG_PATH}")
        return
    
    try:
        # 读取配置
        with open(JSON_CONFIG_PATH, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 获取shelf set
        shelf_set = hou.shelves.shelfSets().get("project")
        if not shelf_set:
            shelf_set = hou.shelves.newShelfSet("project")
        
        # 删除已存在的PIPELINE shelf
        for shelf in shelf_set.shelves():
            if shelf.name() == "PIPELINE":
                shelf_set.destroyShelf(shelf)
                break
        
        # 创建新shelf
        pipeline_shelf = shelf_set.newShelf("PIPELINE", "PIPELINE")
        
        # 添加工具
        for tool_data in config.get("tools", []):
            pipeline_shelf.newTool(
                file_path=None,
                name=tool_data.get("name", "tool"),
                label=tool_data.get("label", "Tool"),
                script=tool_data.get("script", "print('Hello')"),
                language=hou.scriptLanguage.Python,
                icon=tool_data.get("icon", "MISC_python"),
                help=tool_data.get("help", "Pipeline tool"),
                locations=[hou.shelfContext.Desktop]
            )
        
        print(f"PIPELINE shelf创建成功，包含 {len(config.get('tools', []))} 个工具")
        
    except Exception as e:
        hou.ui.displayMessage(f"加载失败: {str(e)}")

# 运行
quick_load_pipeline_shelf()