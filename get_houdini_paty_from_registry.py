"""
Get Ho<PERSON><PERSON> install path from Windows Registry (single function, exact match only).

Usage:
    from get_houdini_paty_from_registry import get_houdini_install_path
    print(get_houdini_install_path())             # -> {version: path, ...}
    print(get_houdini_install_path('20.5.522'))   # -> 'C:\\Program Files\\Side Effects Software\\Houdini 20.5.522' or None

Notes:
- Reads HKLM/HKCU under 'SOFTWARE\\Side Effects Software' and WOW6432Node
- Tries multiple value names and nested keys; validates path exists
- No branch matching: only exact version string will return a single path
"""

import os
import re

try:
    import winreg  # type: ignore
except Exception:
    winreg = None  # type: ignore

_BASES = (
    ("HKLM", r"SOFTWARE\Side Effects Software"),
    ("HKLM", r"SOFTWARE\WOW6432Node\Side Effects Software"),
    ("HKCU", r"SOFTWARE\Side Effects Software"),
)
_VALS = ("InstallPath", "InstallFolder", "InstallDir", "Path", "Location", "")
_NESTED = ("CurrentVersion", "InstallPath", "Settings")
_VER = re.compile(r"(\d+)\.(\d+)(?:\.(\d+))?")


def get_houdini_install_path(version=None):
    # Collect all installations first (dict: version -> path)
    found = {}
    try:
        import platform
        if platform.system().lower() != 'windows' or not winreg:
            return found if version is None else None
    except Exception:
        return found if version is None else None

    def open_hive(name):
        return winreg.HKEY_LOCAL_MACHINE if name == 'HKLM' else winreg.HKEY_CURRENT_USER

    def enum_subkeys(hive, subkey):
        names = []
        try:
            with winreg.OpenKey(hive, subkey) as k:
                i = 0
                while True:
                    try:
                        names.append(winreg.EnumKey(k, i)); i += 1
                    except OSError:
                        break
        except OSError:
            pass
        return names

    def read_values(hive, key_path):
        vals = {}
        try:
            with winreg.OpenKey(hive, key_path) as k:
                i = 0
                while True:
                    try:
                        name, data, _ = winreg.EnumValue(k, i)
                        vals[name] = str(data); i += 1
                    except OSError:
                        break
        except OSError:
            pass
        return vals

    def find_path_in_key(hive, key_path):
        vals = read_values(hive, key_path)
        for n in _VALS:
            if n in vals and vals[n]:
                return vals[n], vals
        for nk in _NESTED:
            sub = read_values(hive, key_path + '\\' + nk)
            for n in _VALS:
                if n in sub and sub[n]:
                    vals.update(sub)
                    return sub[n], vals
        return None, vals

    # read registry
    for hive_name, base in _BASES:
        hive = open_hive(hive_name)
        for sub in enum_subkeys(hive, base):
            if not sub.lower().startswith('houdini'):
                continue
            key_path = base + '\\' + sub
            path, vals = find_path_in_key(hive, key_path)
            if not path or not os.path.isdir(path):
                continue
            ver = (vals.get('Version') or sub.replace('Houdini', '').strip() or
                   (_VER.search(path).group(0) if _VER.search(path) else ''))
            if ver:
                found[ver] = path

    if version is None:
        return found
    version = version.strip()
    return found.get(version)

