"""
Optimized Houdini path lookup via Windows Registry.
Compatible interface with your validated function(s):
- get_houdini_install_path(version=None) -> str | dict
- get_houdini_executable_path(version=None) -> dict | None
- verify_houdini_installation(install_path) -> dict

Notes:
- Searches HKLM/HKCU under Side Effects Software (incl. WOW6432Node)
- Tries multiple value names and common nested keys to find InstallPath
- Exact version match preferred; if version like 'major.minor', returns highest patch in that branch
- No noisy prints inside API functions; demo prints only in __main__
"""

import os
import re
from pathlib import Path

try:
    import winreg  # type: ignore
except Exception:  # Non-Windows or missing
    winreg = None  # type: ignore

_REG_BASES = (
    ("HKLM", r"SOFTWARE\Side Effects Software"),
    ("HKLM", r"SOFTWARE\WOW6432Node\Side Effects Software"),
    ("HKCU", r"SOFTWARE\Side Effects Software"),
)
_VALUE_NAMES = ("InstallPath", "InstallFolder", "InstallDir", "Path", "Location", "")
_NESTED_KEYS = ("CurrentVersion", "InstallPath", "Settings")
_VER_RE = re.compile(r"(\d+)\.(\d+)(?:\.(\d+))?")


def _is_windows():
    import platform
    return platform.system().lower() == "windows"


def _open_hive(hive_name):
    if hive_name == "HKLM":
        return winreg.HKEY_LOCAL_MACHINE
    if hive_name == "HKCU":
        return winreg.HKEY_CURRENT_USER
    return None


def _enum_subkeys(hive, subkey):
    names = []
    try:
        with winreg.OpenKey(hive, subkey) as k:
            i = 0
            while True:
                try:
                    names.append(winreg.EnumKey(k, i))
                    i += 1
                except OSError:
                    break
    except OSError:
        pass
    return names


def _read_values(hive, key_path):
    vals = {}
    try:
        with winreg.OpenKey(hive, key_path) as k:
            i = 0
            while True:
                try:
                    name, data, _ = winreg.EnumValue(k, i)
                    vals[name] = str(data)
                    i += 1
                except OSError:
                    break
    except OSError:
        pass
    return vals


def _find_path_from_key(hive, key_path):
    vals = _read_values(hive, key_path)
    for n in _VALUE_NAMES:
        if n in vals and vals[n]:
            return vals[n], vals
    for nested in _NESTED_KEYS:
        nested_path = key_path + "\\" + nested
        subvals = _read_values(hive, nested_path)
        for n in _VALUE_NAMES:
            if n in subvals and subvals[n]:
                vals.update(subvals)
                return subvals[n], vals
    return None, vals


def _parse_version_tuple(s):
    m = _VER_RE.search(s or "")
    if not m:
        return (0, 0, 0)
    return (int(m.group(1)), int(m.group(2)), int(m.group(3) or "0"))


def _collect_installations():
    """Return dict: version -> install_path, validating path exists."""
    if not _is_windows() or not winreg:
        return {}
    found = {}
    for hive_name, base in _REG_BASES:
        hive = _open_hive(hive_name)
        if hive is None:
            continue
        for sub in _enum_subkeys(hive, base):
            if not sub.lower().startswith("houdini"):
                continue
            key_path = base + "\\" + sub
            path, vals = _find_path_from_key(hive, key_path)
            if not path or not os.path.isdir(path):
                continue
            ver = (vals.get("Version") or sub.replace("Houdini", "").strip() or
                   (_VER_RE.search(path).group(0) if _VER_RE.search(path) else ""))
            found[ver] = path
    return found


def _select_best_in_branch(found, major_minor):
    # major_minor like '20.5' -> choose highest patch '20.5.xxx'
    branch = []
    ma, mi, _ = _parse_version_tuple(major_minor)
    for ver, path in found.items():
        a, b, c = _parse_version_tuple(ver)
        if a == ma and b == mi:
            branch.append(((a, b, c), path))
    if not branch:
        return None
    branch.sort(key=lambda x: x[0], reverse=True)
    return branch[0][1]


def get_houdini_install_path(version=None):
    """If version is None: return dict {version: path}.
    If version is full (e.g. '20.5.522'): return exact match path or None.
    If version is 'major.minor' (e.g. '20.5'): return highest patch in that branch or None.
    """
    found = _collect_installations()
    if version:
        version = version.strip()
        # exact match
        if version in found:
            return found[version]
        # branch best match
        if _VER_RE.fullmatch(version) and version.count(".") == 1:
            return _select_best_in_branch(found, version)
        # prefix fallback (keeps backward behavior if someone passes '20.5 ' etc.)
        for ver, path in found.items():
            if ver.startswith(version):
                return path
        return None
    return found


def get_houdini_executable_path(version=None):
    """Return dict of existing executable paths under <HFS>/bin, or None if not found."""
    if version:
        root = get_houdini_install_path(version)
    else:
        # Try some popular branch if no version given; or return first found
        all_found = get_houdini_install_path()
        root = None
        # Prefer highest version overall
        if all_found:
            items = [(_parse_version_tuple(v), p) for v, p in all_found.items()]
            items.sort(key=lambda x: x[0], reverse=True)
            root = items[0][1]
    if not root:
        return None
    bin_dir = Path(root) / "bin"
    exes = [
        "houdini.exe",
        "houdinifx.exe",
        "houdinicore.exe",
        "houdini-bin.exe",
        "hython.exe",
        "hbatch.exe",
    ]
    result = {}
    for exe in exes:
        p = bin_dir / exe
        if p.exists():
            result[exe.replace(".exe", "")] = str(p)
    return result


def verify_houdini_installation(install_path):
    if not install_path or not os.path.isdir(install_path):
        return {"valid": False, "reason": "路径不存在", "install_path": install_path or ""}
    root = Path(install_path)
    checks = {
        "bin目录": root / "bin",
        "houdini.exe": root / "bin" / "houdini.exe",
        "python目录": root / "python",
        "houdini目录": root / "houdini",
    }
    details = {name: {"path": str(p), "exists": p.exists()} for name, p in checks.items()}
    valid = all(info["exists"] for info in details.values())
    return {"valid": valid, "install_path": str(root), "checks": details}


if __name__ == "__main__":
    print("== Houdini registry lookup demo ==")
    all_versions = get_houdini_install_path()
    if not all_versions:
        print("No Houdini installations found.")
    else:
        for v, p in sorted(all_versions.items(), key=lambda x: _parse_version_tuple(x[0]), reverse=True):
            print(f"{v}: {p}")
        # Demo: choose a version here
        target = next(iter(sorted(all_versions.keys(), key=lambda x: _parse_version_tuple(x), reverse=True)), None)
        if target:
            print(f"\nExecutables for {target}:")
            print(get_houdini_executable_path(target))

