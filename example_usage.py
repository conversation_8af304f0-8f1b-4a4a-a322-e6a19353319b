"""
图片转Base64编码 - 使用示例
演示各种使用方法
"""

from image_to_base64 import image_to_base64, base64_to_image, batch_convert_images, get_image_info
from pathlib import Path

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 假设有一个图片文件
    image_path = "example.png"  # 替换为实际的图片路径
    
    if Path(image_path).exists():
        # 转换为Base64（包含data URL前缀）
        base64_with_prefix = image_to_base64(image_path, include_data_url=True)
        print(f"带前缀的Base64长度: {len(base64_with_prefix) if base64_with_prefix else 0}")
        
        # 转换为Base64（不包含data URL前缀）
        base64_only = image_to_base64(image_path, include_data_url=False)
        print(f"纯Base64长度: {len(base64_only) if base64_only else 0}")
        
        # 显示前100个字符
        if base64_with_prefix:
            print(f"Base64前缀示例: {base64_with_prefix[:100]}...")
    else:
        print(f"图片文件不存在: {image_path}")

def example_web_usage():
    """Web开发中的使用示例"""
    print("\n=== Web开发使用示例 ===")
    
    image_path = "logo.png"  # 替换为实际的图片路径
    
    if Path(image_path).exists():
        # 获取可直接用于HTML的data URL
        data_url = image_to_base64(image_path, include_data_url=True)
        
        if data_url:
            # 生成HTML代码
            html_code = f'<img src="{data_url}" alt="Logo" />'
            print("HTML代码:")
            print(html_code[:200] + "..." if len(html_code) > 200 else html_code)
            
            # 生成CSS代码
            css_code = f'background-image: url("{data_url}");'
            print("\nCSS代码:")
            print(css_code[:200] + "..." if len(css_code) > 200 else css_code)
    else:
        print(f"图片文件不存在: {image_path}")

def example_batch_processing():
    """批量处理示例"""
    print("\n=== 批量处理示例 ===")
    
    # 创建示例目录结构
    images_dir = Path("images")  # 替换为实际的图片目录
    
    if images_dir.exists():
        # 批量转换
        results = batch_convert_images(images_dir, "batch_results.json")
        print(f"批量处理完成，共处理 {len(results)} 个文件")
        
        # 显示结果摘要
        for filename, base64_string in list(results.items())[:3]:  # 只显示前3个
            print(f"  {filename}: {len(base64_string)} 字符")
    else:
        print(f"图片目录不存在: {images_dir}")

def example_image_info():
    """图片信息示例"""
    print("\n=== 图片信息示例 ===")
    
    image_path = "sample.jpg"  # 替换为实际的图片路径
    
    if Path(image_path).exists():
        info = get_image_info(image_path)
        print("图片信息:")
        for key, value in info.items():
            print(f"  {key}: {value}")
    else:
        print(f"图片文件不存在: {image_path}")

def example_reverse_conversion():
    """反向转换示例（Base64转图片）"""
    print("\n=== 反向转换示例 ===")
    
    # 先转换图片为Base64
    source_image = "original.png"  # 替换为实际的图片路径
    
    if Path(source_image).exists():
        base64_string = image_to_base64(source_image, include_data_url=True)
        
        if base64_string:
            # 将Base64转换回图片
            output_path = "converted_back.png"
            success = base64_to_image(base64_string, output_path)
            
            if success:
                print(f"成功将Base64转换回图片: {output_path}")
            else:
                print("转换失败")
    else:
        print(f"源图片文件不存在: {source_image}")

def example_api_usage():
    """API集成示例"""
    print("\n=== API集成示例 ===")
    
    image_path = "api_image.png"  # 替换为实际的图片路径
    
    if Path(image_path).exists():
        # 获取Base64编码
        base64_string = image_to_base64(image_path, include_data_url=False)
        
        if base64_string:
            # 模拟API请求数据
            api_data = {
                "image_data": base64_string,
                "image_format": "png",
                "timestamp": "2024-01-01T00:00:00Z"
            }
            
            print("API请求数据结构:")
            print(f"  image_data: {len(api_data['image_data'])} 字符")
            print(f"  image_format: {api_data['image_format']}")
            print(f"  timestamp: {api_data['timestamp']}")
            
            # 模拟发送到API
            # requests.post('https://api.example.com/upload', json=api_data)
            print("(模拟) 数据已发送到API")
    else:
        print(f"图片文件不存在: {image_path}")

def create_sample_html():
    """创建示例HTML文件"""
    print("\n=== 创建示例HTML文件 ===")
    
    image_path = "demo.png"  # 替换为实际的图片路径
    
    if Path(image_path).exists():
        base64_string = image_to_base64(image_path, include_data_url=True)
        
        if base64_string:
            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Base64图片示例</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .image-container {{ text-align: center; margin: 20px 0; }}
        .base64-info {{ background: #f5f5f5; padding: 10px; margin: 10px 0; }}
    </style>
</head>
<body>
    <h1>Base64图片嵌入示例</h1>
    
    <div class="image-container">
        <h2>嵌入的Base64图片</h2>
        <img src="{base64_string}" alt="Base64 Image" style="max-width: 300px;" />
    </div>
    
    <div class="base64-info">
        <h3>Base64信息</h3>
        <p>Base64字符串长度: {len(base64_string)}</p>
        <p>数据类型: {base64_string.split(',')[0] if ',' in base64_string else 'Unknown'}</p>
    </div>
    
    <div>
        <h3>优点</h3>
        <ul>
            <li>减少HTTP请求</li>
            <li>图片与HTML一起传输</li>
            <li>适合小图标和小图片</li>
        </ul>
        
        <h3>缺点</h3>
        <ul>
            <li>增加HTML文件大小</li>
            <li>不能被浏览器缓存</li>
            <li>Base64编码比原文件大约33%</li>
        </ul>
    </div>
</body>
</html>
"""
            
            with open("base64_demo.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            
            print("示例HTML文件已创建: base64_demo.html")
    else:
        print(f"图片文件不存在: {image_path}")

def main():
    """运行所有示例"""
    print("图片转Base64编码 - 使用示例")
    print("=" * 50)
    
    # 运行各种示例
    example_basic_usage()
    example_web_usage()
    example_batch_processing()
    example_image_info()
    example_reverse_conversion()
    example_api_usage()
    create_sample_html()
    
    print("\n" + "=" * 50)
    print("示例运行完成！")
    print("\n使用提示:")
    print("1. 将示例中的图片路径替换为实际存在的文件")
    print("2. 对于Web开发，建议只对小图片使用Base64编码")
    print("3. 大图片建议使用传统的文件链接方式")
    print("4. Base64编码会增加约33%的数据大小")

if __name__ == "__main__":
    main()
