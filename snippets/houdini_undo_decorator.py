import functools

def houdini_undo(name=None):
    """Houdini 撤销装饰器：将函数体作为一个 Undo 分组。
    用法：
      @houdini_undo()              # 标签=函数名
      @houdini_undo("创建节点")     # 自定义标签
      @houdini_undo                # 直接用（等同 @houdini_undo()）
    """
    # 允许 @houdini_undo 直接用
    if callable(name):
        func = name
        label = func.__name__
        return _wrap_undo(func, label)

    def decorator(func):
        label = name if isinstance(name, str) and name.strip() else func.__name__
        return _wrap_undo(func, label)
    return decorator

def _wrap_undo(func, label):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            import hou
            # 20.5 推荐 API
            if hasattr(hou, "undos") and hasattr(hou.undos, "group"):
                with hou.undos.group(label):
                    return func(*args, **kwargs)
            # 旧版兼容（若存在）
            if hasattr(hou, "UndoGroup"):
                with hou.UndoGroup(label):
                    return func(*args, **kwargs)
        except Exception:
            # 若导入 hou 失败或上下文不支持，直接执行函数
            # （仍然抛出函数内部异常，便于调试）
            pass
        return func(*args, **kwargs)
    return wrapper