# Augment 在 PyCharm 的使用指南（最新版）

面向日常开发者的完整中文手册，涵盖安装、界面、常用工作流、变更应用（含 Keep All）、任务清单、代码检索/修改、安全与隐私、性能优化、常见问题与排障。结合你当前项目（<PERSON><PERSON><PERSON>、3ds Max、PySide6、Windows 注册表脚本等）给出贴近实战的示例。

---

## 1. 安装与首次使用

1. 打开 PyCharm → Settings/Preferences → Plugins → Marketplace 搜索 “Augment”。
2. 点击 Install 并重启 IDE。
3. 首次打开项目后，右下角会显示索引（Indexing/Context）的进度，等待完成可获得最佳效果。
4. 建议：在 Augment 设置里排除大体积或无关目录（如 .venv、node_modules、build、cache）以提升性能。

提示：索引仅用于本地上下文理解，不会修改你的代码。

---

## 2. 界面与核心概念

- Augment Chat 面板：与 AI 对话的主区域（支持内嵌文件/代码上下文）。
- 内联气泡/灯泡：选中代码或在报错处，会出现“解释/改进/生成测试”等快捷操作。
- 变更预览（Diff/Patch）：Augment 提供的修改先以差异形式展示，需你确认后才会落盘。
- Tasklist（任务清单）：将复杂请求拆分为可追踪的子任务，支持 [/] 进行中 / [x] 完成 标记。
- Context/Indexing 状态：显示当前项目的上下文索引进度与范围。

---

## 3. 基础对话与上下文注入

- 选中一段代码 → 右键 “Ask Augment about this”，即可带着高权重上下文发问。
- 在 Chat 输入问题时，明确文件路径/函数名/报错堆栈，Augment 会自动检索并引用相关代码。
- 常用问法：
  - “解释这个函数的输入输出与边界条件，并指出潜在异常。”
  - “把此函数拆成不超过 20 行的子函数，保持原 API 不变。”
  - “为这个注册表查找脚本写 pytest 单测，覆盖异常路径。”

---

## 4. 代码阅读与智能检索

- 语义检索：
  - “在项目里找负责 Houdini HDA 按钮回调的代码。”
  - “哪个函数会写入 shop_materialpath？”
- 结构化定位：
  - “列出 get_houdini_install_path 的调用点，并总结各调用场景差异。”
- 快速跳转：回答中附带的链接可直接打开文件并跳转到对应行号。

---

## 5. 代码生成与安全修改

典型场景：
- “把 Tkinter 界面迁移到 PySide6，尽量不改业务逻辑，补充信号/槽说明。”
- “为 get_houdini_install_path 加入日志开关，不改变默认行为。”
- “编写 Houdini 20.5.522 保存当前视图截图的函数（优先 saveViewToImage，失败回退 Flipbook）。

安全编辑流程：
1) 让 Augment 给出方案 + 变更预览（Diff）。
2) 你审阅差异，必要时让它“只修改某几行/仅触及此文件”。
3) 点击 Apply/Keep（或 Keep All）应用改动（可随时 Undo）。

最小变更原则：
- 明确“不要重排 import/格式化无关行”。
- 明确“保持向后兼容，不改外部接口”。

---

## 6. Keep / Discard / Keep All（重点）

- Keep：接受当前片段（hunk）的修改。
- Discard：丢弃当前片段的修改。
- Keep All：一键应用当前建议范围内的全部修改（适合你已审阅并决定全盘采纳的场景）。

建议流程：
- 先快速扫文件列表与关键片段 → 抽查 1–2 个代表片段 → 再点 Keep All。
- 应用后立刻运行格式化/静态检查/测试，分组小步提交，便于回滚与评审。

不建议直接 Keep All 的场景：跨层/大范围高风险改动、未跑测试。

---

## 7. 任务清单（Tasklist）

何时使用：
- 多文件/跨层的改动，或需要“边查边改”的复杂任务。

用法：
1) 让 Augment 创建“调查/完成/验证”的小清单（保持一个任务进行中）。
2) 完成一个，标记 [x]，再展开下一个，避免一次性大改。

好处：
- 透明可追踪，可随时让你介入调整方向。

---

## 8. 与 Git/PR 的衔接

- 生成规范的 Commit Message 或 PR 描述：
  - “基于当前 diff 给我一个简洁的中文提交信息。”
- 评审辅助：
  - “审查这个 diff，指出潜在风险与更 Pythonic 的实现。”
- 最佳实践：小步提交 + 运行测试，避免一次提交包含大量不相关更改。

---

## 9. 针对你的常见场景（实战示例）

1) Windows 注册表：
- “只保留 get_houdini_install_path 一个函数，精确匹配版本，不做分支匹配，兼容 WOW6432Node。”
- “把 80 行实现拆分为 <20 行的子函数，并补充异常处理。”

2) Houdini：
- “写 @houdini_undo 装饰器，20.5.522 版本优先 hou.undos.group，旧版回退 hou.UndoGroup。”
- “Python 脚本将当前 Scene Viewer 画面保存为 PNG，支持 $HIP 输出路径。”

3) 3ds Max / PySide6：
- “将 Tkinter UI 改写为 PySide6，保持信号/槽逻辑一致，提供样式与尺寸约束。”

---

## 10. 个性化设置、隐私与权限

- 上下文范围：设置中可排除目录/文件类型（.venv、node_modules、build）。
- 隐私：默认本地索引；若使用云端模式，遵守企业策略与访问控制。
- 最小权限：Augment 不会自动提交/执行命令；所有变更需你确认。

---

## 11. 性能与大仓库最佳实践

- 预先排除无关目录，减少索引范围。
- 问法要具体：给出路径/函数名/模块名，检索更快更准。
- 采用“生成 → 审阅 → 应用 → 验证”的小步频率，控制变更规模。

---

## 12. 常见问题与排障

- 上下文缺失/答非所问：确认索引完成；用 “Ask about selection”；明确文件与函数名。
- 改动过大：要求“仅改 X–Y 行/只触及此文件”；请 Augment 输出 patch 而非重排全文件。
- 冲突：如果你在建议生成后手动改过文件，应用时可能冲突。让 Augment 重新生成，或改为逐段 Keep。
- 风格不一致：应用后统一跑格式化工具（black/ruff 等）。

---

## 13. 学习资源与演示建议

- 官方帮助入口：在插件的 Chat 面板与设置页面通常提供 Help/Docs 入口。
- 组织内演示建议：
  - 选 1–2 个真实需求（如“注册表脚本重构”“Houdini 截图函数”），
  - 录制“问题 → 生成方案 → Diff 审阅 → Keep All/分片 Keep → 测试”的全流程 5–10 分钟短视频。
  - 重点展示 Keep All 的适用/禁忌与回滚策略。

如需我为你的项目录制带讲解的示例视频，请告诉我场景与代码位置，我会按你的代码仓结构制作演示脚本与旁白提纲。

---

## 14. 快速参考卡（Cheat Sheet）

- 选中代码 → 右键 → Ask Augment about this（强上下文提问）
- 生成改动 → Diff 预览：Keep / Discard / Keep All
- 小步落地：Apply 后立刻跑检查与测试
- 任务清单：多文件/跨层任务用 Tasklist 分步推进
- 明确限制：只改必要行、不重排、保持兼容

---

最后，建议把这份指南加入项目文档，并根据团队规范附上“提问模板”和“变更审阅清单”。如果你给出团队的代码规范与 CI 要求，我可以再补一版“项目专属的 Augment 最佳实践附录”。

