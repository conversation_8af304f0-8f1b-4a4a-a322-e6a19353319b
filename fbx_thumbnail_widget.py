import sys
import os
from pathlib import Path
from PySide2.QtWidgets import (QApplication, QMainWindow, QListWidget, 
                               QListWidgetItem, QVBoxLayout, QWidget,
                               QHBoxLayout, QLabel, QSplitter)
from PySide2.QtCore import Qt, QThread, Signal, QSize
from PySide2.QtGui import QPixmap, QIcon, QPainter
from PySide2.QtOpenGL import QOpenGLWidget, QOpenGLFramebufferObject
import OpenGL.GL as gl
from OpenGL.GL import *
import numpy as np
import math

try:
    import FbxCommon
    import fbx
    FBX_AVAILABLE = True
except ImportError:
    FBX_AVAILABLE = False
    print("FBX SDK not available. Install FBX Python SDK for full functionality.")

try:
    import open3d as o3d
    OPEN3D_AVAILABLE = True
except ImportError:
    OPEN3D_AVAILABLE = False
    print("Open3D not available. Install with: pip install open3d")

class FBXRenderer:
    """FBX文件渲染器"""
    
    def __init__(self):
        self.vertices = []
        self.indices = []
        self.normals = []
        
    def load_fbx_mesh_data(self, fbx_path):
        """从FBX文件加载网格数据"""
        if not FBX_AVAILABLE:
            return False
            
        try:
            # 初始化FBX SDK
            manager = fbx.FbxManager.Create()
            scene = fbx.FbxScene.Create(manager, "")
            
            # 导入FBX文件
            importer = fbx.FbxImporter.Create(manager, "")
            if not importer.Initialize(fbx_path, -1, manager.GetIOSettings()):
                manager.Destroy()
                return False
            
            importer.Import(scene)
            importer.Destroy()
            
            # 提取网格数据
            self.extract_mesh_data(scene.GetRootNode())
            
            manager.Destroy()
            return len(self.vertices) > 0
            
        except Exception as e:
            print(f"FBX loading error: {e}")
            return False
    
    def extract_mesh_data(self, node):
        """递归提取节点的网格数据"""
        if node.GetNodeAttribute():
            attribute_type = node.GetNodeAttribute().GetAttributeType()
            if attribute_type == fbx.FbxNodeAttribute.eMesh:
                mesh = node.GetNodeAttribute()
                self.process_mesh(mesh, node)
        
        # 递归处理子节点
        for i in range(node.GetChildCount()):
            self.extract_mesh_data(node.GetChild(i))
    
    def process_mesh(self, mesh, node):
        """处理单个网格"""
        # 获取变换矩阵
        transform = node.EvaluateGlobalTransform()
        
        # 获取顶点
        vertex_count = mesh.GetControlPointsCount()
        vertices = mesh.GetControlPoints()
        
        for i in range(vertex_count):
            # 应用变换
            vertex = transform.MultT(vertices[i])
            self.vertices.extend([vertex[0], vertex[1], vertex[2]])
        
        # 获取面索引
        polygon_count = mesh.GetPolygonCount()
        base_index = len(self.vertices) // 3 - vertex_count
        
        for i in range(polygon_count):
            polygon_size = mesh.GetPolygonSize(i)
            if polygon_size >= 3:
                # 三角化多边形
                for j in range(1, polygon_size - 1):
                    self.indices.extend([
                        base_index + mesh.GetPolygonVertex(i, 0),
                        base_index + mesh.GetPolygonVertex(i, j),
                        base_index + mesh.GetPolygonVertex(i, j + 1)
                    ])
    
    def render_to_pixmap(self, size=(128, 128)):
        """渲染到QPixmap"""
        if not self.vertices:
            return self.create_placeholder_thumbnail(size)
        
        # 创建离屏渲染上下文
        app = QApplication.instance()
        if not app:
            app = QApplication([])
        
        # 创建临时OpenGL widget用于渲染
        gl_widget = QOpenGLWidget()
        gl_widget.resize(size[0], size[1])
        gl_widget.makeCurrent()
        
        try:
            # 设置OpenGL状态
            gl.glEnable(gl.GL_DEPTH_TEST)
            gl.glClearColor(0.2, 0.2, 0.2, 1.0)
            gl.glClear(gl.GL_COLOR_BUFFER_BIT | gl.GL_DEPTH_BUFFER_BIT)
            
            # 设置视口
            gl.glViewport(0, 0, size[0], size[1])
            
            # 计算边界框和相机位置
            bbox_min, bbox_max = self.calculate_bounding_box()
            center = [(bbox_min[i] + bbox_max[i]) / 2 for i in range(3)]
            size_vec = [bbox_max[i] - bbox_min[i] for i in range(3)]
            max_size = max(size_vec)
            
            # 设置投影矩阵
            self.setup_projection(size, max_size)
            
            # 设置模型视图矩阵
            self.setup_modelview(center, max_size)
            
            # 渲染网格
            self.render_mesh()
            
            # 读取像素数据
            gl.glFlush()
            pixels = gl.glReadPixels(0, 0, size[0], size[1], gl.GL_RGB, gl.GL_UNSIGNED_BYTE)
            
            # 转换为QPixmap
            image_data = np.frombuffer(pixels, dtype=np.uint8).reshape(size[1], size[0], 3)
            image_data = np.flipud(image_data)  # OpenGL坐标系翻转
            
            # 创建QPixmap
            from PySide2.QtGui import QImage
            qimage = QImage(image_data.data, size[0], size[1], QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(qimage)
            
            return pixmap
            
        except Exception as e:
            print(f"Rendering error: {e}")
            return self.create_placeholder_thumbnail(size)
        finally:
            gl_widget.doneCurrent()
    
    def calculate_bounding_box(self):
        """计算边界框"""
        if not self.vertices:
            return [0, 0, 0], [1, 1, 1]
        
        vertices_array = np.array(self.vertices).reshape(-1, 3)
        bbox_min = vertices_array.min(axis=0).tolist()
        bbox_max = vertices_array.max(axis=0).tolist()
        return bbox_min, bbox_max
    
    def setup_projection(self, size, max_size):
        """设置投影矩阵"""
        gl.glMatrixMode(gl.GL_PROJECTION)
        gl.glLoadIdentity()
        
        aspect = size[0] / size[1]
        fov = 45.0
        near = max_size * 0.1
        far = max_size * 10.0
        
        # 透视投影
        f = 1.0 / math.tan(math.radians(fov) / 2.0)
        projection = [
            f / aspect, 0, 0, 0,
            0, f, 0, 0,
            0, 0, (far + near) / (near - far), (2 * far * near) / (near - far),
            0, 0, -1, 0
        ]
        gl.glLoadMatrixf(projection)
    
    def setup_modelview(self, center, max_size):
        """设置模型视图矩阵"""
        gl.glMatrixMode(gl.GL_MODELVIEW)
        gl.glLoadIdentity()
        
        # 相机位置
        camera_distance = max_size * 2.0
        camera_pos = [center[0], center[1], center[2] + camera_distance]
        
        # 查看矩阵
        gl.glTranslatef(-camera_pos[0], -camera_pos[1], -camera_pos[2])
        gl.glTranslatef(center[0], center[1], center[2])
    
    def render_mesh(self):
        """渲染网格"""
        if not self.vertices or not self.indices:
            return
        
        # 启用顶点数组
        gl.glEnableClientState(gl.GL_VERTEX_ARRAY)
        
        # 设置顶点数据
        vertices_array = np.array(self.vertices, dtype=np.float32)
        gl.glVertexPointer(3, gl.GL_FLOAT, 0, vertices_array)
        
        # 设置材质
        gl.glColor3f(0.7, 0.7, 0.9)  # 浅蓝色
        
        # 绘制三角形
        if self.indices:
            indices_array = np.array(self.indices, dtype=np.uint32)
            gl.glDrawElements(gl.GL_TRIANGLES, len(self.indices), gl.GL_UNSIGNED_INT, indices_array)
        else:
            gl.glDrawArrays(gl.GL_TRIANGLES, 0, len(self.vertices) // 3)
        
        # 禁用顶点数组
        gl.glDisableClientState(gl.GL_VERTEX_ARRAY)
    
    def create_placeholder_thumbnail(self, size):
        """创建占位符缩略图"""
        pixmap = QPixmap(size[0], size[1])
        pixmap.fill(Qt.lightGray)
        
        painter = QPainter(pixmap)
        painter.setPen(Qt.darkGray)
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "FBX")
        painter.end()
        
        return pixmap

class Open3DRenderer:
    """使用Open3D的备选渲染器"""
    
    def render_fbx_with_open3d(self, fbx_path, size=(128, 128)):
        """使用Open3D渲染FBX文件"""
        if not OPEN3D_AVAILABLE:
            return self.create_placeholder_thumbnail(size)
        
        try:
            # 尝试加载网格（Open3D支持多种格式）
            mesh = o3d.io.read_triangle_mesh(fbx_path)
            
            if len(mesh.vertices) == 0:
                return self.create_placeholder_thumbnail(size)
            
            # 计算法线
            mesh.compute_vertex_normals()
            
            # 创建可视化器
            vis = o3d.visualization.Visualizer()
            vis.create_window(width=size[0], height=size[1], visible=False)
            
            # 添加网格
            vis.add_geometry(mesh)
            
            # 设置相机
            ctr = vis.get_view_control()
            ctr.set_zoom(0.8)
            
            # 渲染
            vis.poll_events()
            vis.update_renderer()
            
            # 捕获图像
            image = vis.capture_screen_float_buffer(False)
            vis.destroy_window()
            
            # 转换为QPixmap
            image_np = np.asarray(image) * 255
            image_np = image_np.astype(np.uint8)
            
            from PySide2.QtGui import QImage
            qimage = QImage(image_np.data, size[0], size[1], QImage.Format_RGB888)
            return QPixmap.fromImage(qimage)
            
        except Exception as e:
            print(f"Open3D rendering error: {e}")
            return self.create_placeholder_thumbnail(size)
    
    def create_placeholder_thumbnail(self, size):
        """创建占位符缩略图"""
        pixmap = QPixmap(size[0], size[1])
        pixmap.fill(Qt.lightGray)
        
        painter = QPainter(pixmap)
        painter.setPen(Qt.darkGray)
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "FBX")
        painter.end()
        
        return pixmap

class FBXThumbnailGenerator(QThread):
    """FBX缩略图生成线程"""
    thumbnail_ready = Signal(str, QPixmap)
    
    def __init__(self, fbx_path, size=(128, 128)):
        super().__init__()
        self.fbx_path = fbx_path
        self.size = size
    
    def run(self):
        try:
            thumbnail = self.generate_fbx_thumbnail(self.fbx_path, self.size)
            if thumbnail:
                self.thumbnail_ready.emit(self.fbx_path, thumbnail)
        except Exception as e:
            print(f"Error generating thumbnail for {self.fbx_path}: {e}")
    
    def generate_fbx_thumbnail(self, fbx_path, size):
        """生成FBX文件的缩略图"""
        # 优先使用FBX SDK渲染器
        if FBX_AVAILABLE:
            renderer = FBXRenderer()
            if renderer.load_fbx_mesh_data(fbx_path):
                return renderer.render_to_pixmap(size)
        
        # 备选：使用Open3D
        if OPEN3D_AVAILABLE:
            renderer = Open3DRenderer()
            return renderer.render_fbx_with_open3d(fbx_path, size)
        
        # 最后备选：占位符
        return self.create_placeholder_thumbnail(size)
    
    def create_placeholder_thumbnail(self, size):
        """创建占位符缩略图"""
        pixmap = QPixmap(size[0], size[1])
        pixmap.fill(Qt.lightGray)
        
        painter = QPainter(pixmap)
        painter.setPen(Qt.darkGray)
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "FBX")
        painter.end()
        
        return pixmap