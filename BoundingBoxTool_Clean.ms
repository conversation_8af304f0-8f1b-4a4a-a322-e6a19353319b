-- 3ds Max Bounding Box and Volume Display Tool (Clean Version)
-- Author: Assistant
-- Description: Displays bounding box and volume information for selected objects

-- Global variables
global boundingBoxHelper = undefined
global volumeText = undefined
global bbTool_rollout
global bbToolCallbackActive = false

-- Function to calculate volume from bounding box
function calculateVolume obj =
(
    if obj != undefined then
    (
        local bbox = nodeGetBoundingBox obj (matrix3 1)
        local dimensions = bbox[2] - bbox[1]
        local volume = dimensions.x * dimensions.y * dimensions.z
        return volume
    )
    else
        return 0.0
)

-- Function to create bounding box visualization
function createBoundingBoxHelper obj =
(
    if obj != undefined then
    (
        if boundingBoxHelper != undefined then
            delete boundingBoxHelper
        
        local bbox = nodeGetBoundingBox obj (matrix3 1)
        local center = (bbox[1] + bbox[2]) / 2
        local dimensions = bbox[2] - bbox[1]
        
        boundingBoxHelper = Box length:dimensions.x width:dimensions.y height:dimensions.z
        boundingBoxHelper.pos = center
        boundingBoxHelper.wirecolor = color 255 0 0
        boundingBoxHelper.boxmode = true
        boundingBoxHelper.name = "BoundingBox_Helper"
        boundingBoxHelper.renderable = false
        
        return boundingBoxHelper
    )
    return undefined
)

-- Function to create volume text display
function createVolumeText obj =
(
    if obj != undefined then
    (
        if volumeText != undefined then
            delete volumeText
        
        local volume = calculateVolume obj
        local bbox = nodeGetBoundingBox obj (matrix3 1)
        local center = (bbox[1] + bbox[2]) / 2
        local dimensions = bbox[2] - bbox[1]
        
        volumeText = Text()
        local volumeStr = volume as string
        local dimXStr = dimensions.x as string
        local dimYStr = dimensions.y as string
        local dimZStr = dimensions.z as string
        
        volumeText.text = "Volume: " + volumeStr + " units^3\nDimensions: " + dimXStr + " x " + dimYStr + " x " + dimZStr
        volumeText.size = 10
        volumeText.pos = [center.x, center.y, bbox[2].z + 20]
        volumeText.wirecolor = color 0 255 0
        volumeText.name = "Volume_Text"
        
        return volumeText
    )
    return undefined
)

-- Function to update display for current selection
function updateBoundingBoxDisplay =
(
    if selection.count > 0 then
    (
        local obj = selection[1]
        createBoundingBoxHelper obj
        createVolumeText obj
        
        if bbTool_rollout != undefined then
        (
            local volume = calculateVolume obj
            local bbox = nodeGetBoundingBox obj (matrix3 1)
            local dimensions = bbox[2] - bbox[1]
            
            bbTool_rollout.lblObjectName.text = "Object: " + obj.name
            bbTool_rollout.lblVolume.text = "Volume: " + (formattedPrint volume format:"f") + " units^3"
            bbTool_rollout.lblDimensions.text = "Dimensions: " + 
                (formattedPrint dimensions.x format:"f") + " x " + 
                (formattedPrint dimensions.y format:"f") + " x " + 
                (formattedPrint dimensions.z format:"f")
        )
        
        print ("Object: " + obj.name)
        print ("Volume: " + (volume as string) + " units^3")
    )
    else
    (
        if boundingBoxHelper != undefined then
        (
            delete boundingBoxHelper
            boundingBoxHelper = undefined
        )
        if volumeText != undefined then
        (
            delete volumeText
            volumeText = undefined
        )
        
        if bbTool_rollout != undefined then
        (
            bbTool_rollout.lblObjectName.text = "Object: None selected"
            bbTool_rollout.lblVolume.text = "Volume: 0.0 units^3"
            bbTool_rollout.lblDimensions.text = "Dimensions: 0 x 0 x 0"
        )
        
        print "No object selected"
    )
)

-- Function to clear all helpers
function clearBoundingBoxDisplay =
(
    if boundingBoxHelper != undefined then
    (
        delete boundingBoxHelper
        boundingBoxHelper = undefined
    )
    if volumeText != undefined then
    (
        delete volumeText
        volumeText = undefined
    )
    print "Display cleared"
)

-- Function to register callback
function registerCallback =
(
    if not bbToolCallbackActive then
    (
        callbacks.addScript #selectionSetChanged "if bbTool_rollout != undefined and bbTool_rollout.chkAutoUpdate.checked then updateBoundingBoxDisplay()" id:#bbToolCallback
        bbToolCallbackActive = true
        print "Callback registered"
    )
    else
    (
        print "Callback already active"
    )
)

-- Function to unregister callback
function unregisterCallback =
(
    if bbToolCallbackActive then
    (
        callbacks.removeScripts id:#bbToolCallback
        bbToolCallbackActive = false
        print "Callback unregistered"
    )
    else
    (
        print "Callback already inactive"
    )
)

-- Rollout for the tool interface
rollout bbTool_rollout "Bounding Box Tool" width:300 height:250
(
    label lblTitle "Bounding Box & Volume Tool"
    label lblObjectName "Object: None selected"
    label lblVolume "Volume: 0.0 units^3"
    label lblDimensions "Dimensions: 0 x 0 x 0"
    
    button btnUpdate "Update Display" width:120 height:30 across:2
    button btnClear "Clear Display" width:120 height:30
    
    checkbox chkAutoUpdate "Auto Update on Selection" checked:true
    
    button btnRegisterCallback "Register Callback" width:130 height:25 across:2
    button btnUnregisterCallback "Unregister Callback" width:130 height:25
    
    button btnClose "Close Tool" width:280 height:25
    
    on btnUpdate pressed do updateBoundingBoxDisplay()
    on btnClear pressed do
    (
        clearBoundingBoxDisplay()
        lblObjectName.text = "Object: None selected"
        lblVolume.text = "Volume: 0.0 units^3"
        lblDimensions.text = "Dimensions: 0 x 0 x 0"
    )
    on btnRegisterCallback pressed do registerCallback()
    on btnUnregisterCallback pressed do unregisterCallback()
    on btnClose pressed do
    (
        clearBoundingBoxDisplay()
        unregisterCallback()
        destroyDialog bbTool_rollout
    )
    on bbTool_rollout close do
    (
        clearBoundingBoxDisplay()
        unregisterCallback()
    )
)

-- Function to start the tool
function startBoundingBoxTool =
(
    try(destroyDialog bbTool_rollout) catch()
    unregisterCallback()
    createDialog bbTool_rollout
    registerCallback()
    updateBoundingBoxDisplay()
)

-- Start the tool
startBoundingBoxTool()

print "Bounding Box Tool loaded successfully!"
print "Select an object to display its bounding box and volume information."
