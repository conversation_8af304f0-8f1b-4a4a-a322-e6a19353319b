"""
完整的Ho<PERSON>ni启动解决方案
将此文件保存为: $HOUDINI_USER_PREF_DIR/scripts/123.py
"""

import hou
import os
import sys
import json

class PipelineStartup:
    def __init__(self):
        self.config_loaded = False
        self.shelf_loaded = False
        
    def load_config(self):
        """加载Pipeline配置"""
        config_paths = [
            os.path.join(hou.getenv("HOUDINI_USER_PREF_DIR"), "pipeline_config.json"),
            "C:/pipeline/houdini/config.json"
        ]
        
        for config_path in config_paths:
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r') as f:
                        self.config = json.load(f)
                    self.config_loaded = True
                    print(f"Pipeline配置加载成功: {config_path}")
                    return True
                except Exception as e:
                    print(f"配置加载失败: {e}")
        
        # 使用默认配置
        self.config = {
            "pipeline_root": "C:/pipeline",
            "auto_load_shelf": True,
            "shelf_config": "pipeline_tools.json"
        }
        return False
    
    def setup_environment(self):
        """设置环境"""
        if self.config_loaded:
            pipeline_root = self.config.get("pipeline_root", "C:/pipeline")
            
            # 添加Python路径
            python_path = os.path.join(pipeline_root, "python")
            if os.path.exists(python_path) and python_path not in sys.path:
                sys.path.insert(0, python_path)
            
            # 设置环境变量
            os.environ["PIPELINE_ROOT"] = pipeline_root
            
            print("Pipeline环境设置完成")
    
    def load_shelf_delayed(self):
        """延迟加载shelf"""
        if self.config.get("auto_load_shelf", True) and not self.shelf_loaded:
            try:
                # 你的shelf加载代码
                shelf_config = self.config.get("shelf_config", "pipeline_tools.json")
                config_path = os.path.join(self.config.get("pipeline_root", "C:/pipeline"), shelf_config)
                
                if os.path.exists(config_path):
                    self.create_pipeline_shelf(config_path)
                    self.shelf_loaded = True
                    print("Pipeline shelf加载完成")
                
            except Exception as e:
                print(f"Shelf加载失败: {e}")
    
    def create_pipeline_shelf(self, json_path):
        """创建Pipeline shelf"""
        with open(json_path, 'r', encoding='utf-8') as f:
            tools_config = json.load(f)
        
        # 获取或创建shelf set
        shelf_set = hou.shelves.shelfSets().get("project")
        if not shelf_set:
            shelf_set = hou.shelves.newShelfSet("project")
        
        # 删除已存在的PIPELINE shelf
        for shelf in shelf_set.shelves():
            if shelf.name() == "PIPELINE":
                shelf_set.destroyShelf(shelf)
                break
        
        # 创建新shelf
        pipeline_shelf = shelf_set.newShelf("PIPELINE", "PIPELINE")
        
        # 添加工具
        for tool_data in tools_config.get("tools", []):
            pipeline_shelf.newTool(
                file_path=None,
                name=tool_data.get("name", "tool"),
                label=tool_data.get("label", "Tool"),
                script=tool_data.get("script", "print('Hello')"),
                language=hou.scriptLanguage.Python,
                icon=tool_data.get("icon", "MISC_python"),
                help=tool_data.get("help", "Pipeline tool"),
                locations=[hou.shelfContext.Desktop]
            )
    
    def run(self):
        """运行启动流程"""
        print("="*50)
        print("Pipeline启动脚本开始执行...")
        print("="*50)
        
        # 加载配置
        self.load_config()
        
        # 设置环境
        self.setup_environment()
        
        # 延迟加载shelf
        def delayed_load():
            self.load_shelf_delayed()
        
        # 使用定时器延迟执行（等待UI完全加载）
        hou.ui.addEventLoopCallback(delayed_load)
        
        print("Pipeline启动脚本执行完成")

# 创建并运行启动器
pipeline_startup = PipelineStartup()
pipeline_startup.run()