"""
Maya脚本：查找shadingEngine节点之前连接的所有file节点
"""

import maya.cmds as cmds

def find_file_nodes_before_shading_engine(shading_engine=None):
    """
    查找指定shadingEngine节点之前连接的所有file节点
    
    Args:
        shading_engine (str): shadingEngine节点名称，如果为None则查找所有
    
    Returns:
        dict: {shadingEngine: [file_nodes]}
    """
    results = {}
    
    # 如果没有指定，获取所有shadingEngine节点
    if shading_engine is None:
        shading_engines = cmds.ls(type='shadingEngine')
    else:
        shading_engines = [shading_engine] if cmds.objExists(shading_engine) else []
    
    for sg in shading_engines:
        file_nodes = []
        
        # 获取连接到shadingEngine的所有上游节点
        upstream_nodes = cmds.listHistory(sg, pruneDagObjects=True)
        
        if upstream_nodes:
            # 过滤出file节点
            file_nodes = [node for node in upstream_nodes if cmds.nodeType(node) == 'file']
        
        results[sg] = file_nodes
        
        # 打印结果
        if file_nodes:
            print(f"ShadingEngine: {sg}")
            for file_node in file_nodes:
                file_path = cmds.getAttr(f"{file_node}.fileTextureName")
                print(f"  - {file_node}: {file_path}")
        else:
            print(f"ShadingEngine: {sg} - 没有找到file节点")
    
    return results

def find_file_nodes_detailed():
    """
    详细查找file节点，包括连接路径信息
    """
    results = {}
    shading_engines = cmds.ls(type='shadingEngine')
    
    for sg in shading_engines:
        sg_info = {
            'file_nodes': [],
            'connections': []
        }
        
        # 获取所有上游连接
        upstream_nodes = cmds.listHistory(sg, pruneDagObjects=True)
        
        if upstream_nodes:
            file_nodes = [node for node in upstream_nodes if cmds.nodeType(node) == 'file']
            
            for file_node in file_nodes:
                file_info = {
                    'node': file_node,
                    'file_path': cmds.getAttr(f"{file_node}.fileTextureName"),
                    'connections_to_sg': []
                }
                
                # 查找file节点到shadingEngine的连接路径
                connections = cmds.listConnections(file_node, plugs=True, connections=True)
                if connections:
                    # 成对处理连接
                    for i in range(0, len(connections), 2):
                        source_plug = connections[i]
                        dest_plug = connections[i+1] if i+1 < len(connections) else None
                        if dest_plug:
                            file_info['connections_to_sg'].append((source_plug, dest_plug))
                
                sg_info['file_nodes'].append(file_info)
        
        results[sg] = sg_info
    
    return results

def find_file_nodes_by_material():
    """
    按材质分组查找file节点
    """
    results = {}
    
    # 获取所有材质节点
    materials = cmds.ls(materials=True)
    
    for material in materials:
        # 获取材质连接的shadingEngine
        shading_engines = cmds.listConnections(material, type='shadingEngine')
        
        if shading_engines:
            # 获取材质的上游file节点
            upstream_nodes = cmds.listHistory(material, pruneDagObjects=True)
            file_nodes = [node for node in upstream_nodes if cmds.nodeType(node) == 'file']
            
            results[material] = {
                'shading_engines': shading_engines,
                'file_nodes': file_nodes
            }
    
    return results

def print_detailed_results():
    """
    打印详细的查找结果
    """
    print("="*60)
    print("查找shadingEngine之前的所有file节点")
    print("="*60)
    
    # 方法1：基本查找
    print("\n方法1：基本查找")
    print("-"*40)
    basic_results = find_file_nodes_before_shading_engine()
    
    # 方法2：详细查找
    print("\n方法2：详细连接信息")
    print("-"*40)
    detailed_results = find_file_nodes_detailed()
    
    for sg, info in detailed_results.items():
        if info['file_nodes']:
            print(f"\nShadingEngine: {sg}")
            for file_info in info['file_nodes']:
                print(f"  File节点: {file_info['node']}")
                print(f"    文件路径: {file_info['file_path']}")
                if file_info['connections_to_sg']:
                    print("    连接:")
                    for source, dest in file_info['connections_to_sg']:
                        print(f"      {source} -> {dest}")
    
    # 方法3：按材质分组
    print("\n方法3：按材质分组")
    print("-"*40)
    material_results = find_file_nodes_by_material()
    
    for material, info in material_results.items():
        if info['file_nodes']:
            print(f"\n材质: {material}")
            print(f"  ShadingEngine: {info['shading_engines']}")
            print(f"  File节点: {info['file_nodes']}")

def find_specific_file_connections(file_attribute="fileTextureName"):
    """
    查找特定属性的file节点连接
    
    Args:
        file_attribute (str): 要查找的file节点属性
    """
    file_nodes = cmds.ls(type='file')
    results = {}
    
    for file_node in file_nodes:
        # 获取file节点的输出连接
        output_connections = cmds.listConnections(file_node, source=False, plugs=True)
        
        if output_connections:
            # 查找连接到shadingEngine的路径
            for connection in output_connections:
                # 递归查找是否最终连接到shadingEngine
                downstream_nodes = cmds.listHistory(connection, future=True, pruneDagObjects=True)
                shading_engines = [node for node in downstream_nodes if cmds.nodeType(node) == 'shadingEngine']
                
                if shading_engines:
                    file_path = cmds.getAttr(f"{file_node}.{file_attribute}")
                    results[file_node] = {
                        'file_path': file_path,
                        'connected_to': shading_engines,
                        'output_connection': connection
                    }
    
    return results

# 主函数
def main():
    """
    主函数：运行所有查找方法
    """
    print_detailed_results()
    
    print("\n" + "="*60)
    print("特定连接查找")
    print("="*60)
    
    specific_results = find_specific_file_connections()
    for file_node, info in specific_results.items():
        print(f"\nFile节点: {file_node}")
        print(f"  文件路径: {info['file_path']}")
        print(f"  连接到ShadingEngine: {info['connected_to']}")
        print(f"  输出连接: {info['output_connection']}")

if __name__ == "__main__":
    main()