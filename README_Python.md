# 3ds Max 2023 Bounding Box Tool (Python版本)

专为3ds Max 2023设计的Python工具，用于显示模型的bounding box和体积信息。

## 🎯 功能特性

1. **选中模型显示BoundingBox** - 红色线框显示对象边界
2. **显示模型体积** - 绿色文本显示体积和尺寸信息
3. **Clear按钮** - 清除显示并注销所有callback
4. **Display按钮** - 显示boundingbox和体积，并注册callback

## 🔧 技术说明

- **适用版本**: 3ds Max 2023
- **语言**: Python
- **模块**: 使用 `pymxs` 替代已移除的 `MaxPlus`
- **界面**: Tkinter GUI

## 📦 安装和使用

### 方法1: 直接运行
1. 将 `bounding_box_tool.py` 放到任意位置
2. 在3ds Max中打开MAXScript监听器
3. 执行以下命令：
```maxscript
python.ExecuteFile "C:/path/to/bounding_box_tool.py"
```

### 方法2: 通过Python编辑器
1. 在3ds Max中打开 Scripting → Python Editor
2. 打开 `bounding_box_tool.py` 文件
3. 点击 Execute 或按 Ctrl+E

### 方法3: 添加到启动脚本
将文件放到3ds Max的Python启动目录中自动加载。

## 🎮 使用方法

### 基本操作
1. **启动工具**: 运行Python脚本，会弹出工具窗口
2. **选择对象**: 在3ds Max中选择要分析的模型
3. **显示BoundingBox**: 点击 "Display BoundingBox" 按钮
4. **清除显示**: 点击 "Clear Display" 按钮

### 功能详解

#### Display BoundingBox按钮
- 为选中的对象创建红色线框bounding box
- 显示绿色文本信息（体积和尺寸）
- 自动注册selection change callback
- 选择变化时自动更新显示

#### Clear Display按钮
- 删除所有创建的辅助对象
- 注销所有callback
- 清空界面信息
- 重置工具状态

## 📊 显示信息

### GUI界面显示
- **Object**: 当前选中对象的名称
- **Volume**: 计算出的体积（单位³）
- **Dimensions**: X、Y、Z三个方向的尺寸

### 3D视窗显示
- **红色线框**: BoundingBox边界框
- **绿色文本**: 体积和尺寸信息，位于对象上方

## 🔍 技术实现

### 核心功能
```python
# 获取选中对象
obj = rt.selection[0]

# 计算bounding box (世界坐标系)
bbox = rt.nodeGetBoundingBox(obj, rt.Matrix3(1))

# 创建可视化辅助对象
box_helper = rt.Box()
text_helper = rt.Text()
```

### Callback管理
```python
# 注册callback
rt.callbacks.addScript(rt.name("selectionSetChanged"), callback_script, id=rt.name("bboxToolCallback"))

# 注销callback
rt.callbacks.removeScripts(id=rt.name("bboxToolCallback"))
```

## ⚠️ 注意事项

1. **3ds Max版本**: 仅适用于3ds Max 2023及以上版本
2. **Python环境**: 确保3ds Max的Python环境正常
3. **对象类型**: 适用于所有几何体对象
4. **性能**: 复杂场景中建议手动控制更新
5. **清理**: 关闭工具前建议点击Clear按钮

## 🐛 故障排除

### 工具无法启动
- 检查Python脚本路径是否正确
- 确认3ds Max 2023的Python环境
- 查看MAXScript监听器的错误信息

### BoundingBox位置不正确
- 工具使用世界坐标系计算，应该是准确的
- 如有问题，检查对象是否有复杂的层级结构

### Callback不工作
- 点击"Display BoundingBox"按钮重新注册
- 检查是否有其他脚本冲突

### GUI界面问题
- 确保系统支持Tkinter
- 重新运行脚本

## 📝 代码结构

```
bounding_box_tool.py
├── BoundingBoxTool类
│   ├── GUI创建和管理
│   ├── BoundingBox计算
│   ├── 可视化对象创建
│   ├── Callback管理
│   └── 事件处理
└── 启动函数
```

## 🔄 更新日志

### v1.0
- 初始版本
- 支持3ds Max 2023
- 使用pymxs替代MaxPlus
- Tkinter GUI界面
- 完整的callback管理

---

**使用愉快！如有问题请反馈。**
