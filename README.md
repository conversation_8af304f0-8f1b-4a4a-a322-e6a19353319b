# 3ds Max Bounding Box Tool

一个用于在3ds Max中显示模型bounding box和体积信息的工具。

## 功能特性

- **可视化Bounding Box**: 在视窗中显示选中对象的红色线框bounding box
- **体积计算**: 自动计算并显示对象的体积
- **尺寸信息**: 显示对象在X、Y、Z轴上的尺寸
- **实时更新**: 支持选择变化时自动更新显示
- **友好界面**: 提供简洁的用户界面控制

## 安装和使用

### 安装方法

1. 将 `BoundingBoxTool.ms` 文件复制到3ds Max的scripts目录
2. 或者直接在3ds Max中打开MAXScript编辑器，加载并运行该脚本

### 使用步骤

1. **启动工具**:
   - 在3ds Max中，打开MAXScript → MAXScript Editor
   - 打开 `BoundingBoxTool.ms` 文件
   - 点击 "Evaluate All" 或按 Ctrl+E 运行脚本

2. **使用工具**:
   - 工具启动后会显示一个对话框
   - 选择场景中的任意对象
   - 工具会自动显示：
     - 红色的bounding box线框
     - 绿色的体积和尺寸文本信息
     - 界面中的详细数据

3. **控制选项**:
   - **Update Display**: 手动更新显示
   - **Clear Display**: 清除所有显示元素
   - **Auto Update on Selection**: 勾选后选择变化时自动更新
   - **Close Tool**: 关闭工具并清理所有显示元素

## 工具界面说明

- **Object**: 显示当前选中的对象名称
- **Volume**: 显示计算出的体积（单位³）
- **Dimensions**: 显示X、Y、Z三个方向的尺寸

## 显示元素

### Bounding Box Helper
- 红色线框显示对象的bounding box
- 名称: "BoundingBox_Helper"
- 不参与渲染，仅用于显示

### Volume Text
- 绿色文本显示体积和尺寸信息
- 位置在对象上方
- 名称: "Volume_Text"

## 注意事项

1. **单位**: 体积计算使用当前3ds Max场景的单位设置
2. **性能**: 对于复杂场景，建议关闭自动更新以提高性能
3. **清理**: 关闭工具时会自动清理所有创建的辅助对象
4. **多选**: 当前版本仅支持显示第一个选中对象的信息

## 技术细节

- 使用MAXScript编写
- 通过 `nodeGetBoundingBox` 函数获取对象边界框
- 创建Box helper和Text对象进行可视化显示
- 使用回调函数监听选择变化事件

## 扩展功能建议

- 支持多对象同时显示
- 添加不同的显示模式（线框、实体等）
- 支持自定义颜色和文本大小
- 添加导出功能，将数据保存到文件

## 故障排除

如果工具无法正常工作：

1. 确保3ds Max版本支持MAXScript
2. 检查是否有对象被选中
3. 尝试手动点击"Update Display"按钮
4. 重新运行脚本

## 版本信息

- 版本: 1.0
- 兼容性: 3ds Max 2018及以上版本
- 语言: MAXScript

---

**使用愉快！如有问题或建议，欢迎反馈。**
