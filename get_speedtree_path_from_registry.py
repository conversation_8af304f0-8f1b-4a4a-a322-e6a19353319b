"""
通过注册表获取SpeedTree安装路径
支持多个版本的SpeedTree检测
"""

import winreg
import os
from pathlib import Path

def get_speedtree_install_path(version=None):
    """
    通过注册表获取SpeedTree安装路径
    
    Args:
        version (str): 指定版本号，如 "10.0" 或 "10.0.1"，None表示查找所有版本
    
    Returns:
        str or dict: 如果指定版本返回路径字符串，否则返回版本字典
    """
    
    # SpeedTree在注册表中的可能位置
    registry_paths = [
        # 64位系统上的64位程序
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Interactive Data Visualization"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\SpeedTree"),
        # 64位系统上的32位程序
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Interactive Data Visualization"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\SpeedTree"),
        # 当前用户
        (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Interactive Data Visualization"),
        (winreg.HKEY_CURRENT_USER, r"SOFTWARE\SpeedTree"),
        # Uninstall信息
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"),
    ]
    
    found_versions = {}
    
    for hkey, base_path in registry_paths:
        try:
            with winreg.OpenKey(hkey, base_path) as key:
                # 如果是Uninstall路径，需要枚举子键查找SpeedTree
                if "Uninstall" in base_path:
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            
                            # 检查是否是SpeedTree相关的键
                            if "speedtree" in subkey_name.lower() or "speed tree" in subkey_name.lower():
                                try:
                                    uninstall_path = f"{base_path}\\{subkey_name}"
                                    with winreg.OpenKey(hkey, uninstall_path) as uninstall_key:
                                        
                                        # 获取显示名称和安装路径
                                        display_name = None
                                        install_path = None
                                        
                                        try:
                                            display_name, _ = winreg.QueryValueEx(uninstall_key, "DisplayName")
                                        except FileNotFoundError:
                                            pass
                                        
                                        try:
                                            install_path, _ = winreg.QueryValueEx(uninstall_key, "InstallLocation")
                                        except FileNotFoundError:
                                            try:
                                                install_path, _ = winreg.QueryValueEx(uninstall_key, "InstallPath")
                                            except FileNotFoundError:
                                                pass
                                        
                                        if install_path and os.path.exists(install_path):
                                            # 从显示名称中提取版本号
                                            version_str = extract_speedtree_version(display_name or subkey_name)
                                            if version_str:
                                                found_versions[version_str] = install_path
                                                print(f"找到 SpeedTree {version_str}: {install_path}")
                                        
                                except Exception:
                                    continue
                            
                            i += 1
                            
                        except OSError:
                            break
                
                else:
                    # 直接查找SpeedTree相关键
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            
                            if "speedtree" in subkey_name.lower() or "speed tree" in subkey_name.lower():
                                try:
                                    speedtree_path = f"{base_path}\\{subkey_name}"
                                    with winreg.OpenKey(hkey, speedtree_path) as speedtree_key:
                                        
                                        # 尝试获取安装路径
                                        install_path = None
                                        try:
                                            install_path, _ = winreg.QueryValueEx(speedtree_key, "InstallPath")
                                        except FileNotFoundError:
                                            try:
                                                install_path, _ = winreg.QueryValueEx(speedtree_key, "InstallDir")
                                            except FileNotFoundError:
                                                try:
                                                    install_path, _ = winreg.QueryValueEx(speedtree_key, "")
                                                except FileNotFoundError:
                                                    pass
                                        
                                        if install_path and os.path.exists(install_path):
                                            # 提取版本号
                                            version_str = extract_speedtree_version(subkey_name)
                                            if version_str:
                                                found_versions[version_str] = install_path
                                                print(f"找到 SpeedTree {version_str}: {install_path}")
                                            
                                except Exception:
                                    continue
                            
                            i += 1
                            
                        except OSError:
                            break
                        
        except Exception:
            continue
    
    # 如果指定了版本，返回对应路径
    if version:
        # 精确匹配
        if version in found_versions:
            return found_versions[version]
        
        # 模糊匹配（例如输入"10.0"匹配"10.0.1"）
        for ver, path in found_versions.items():
            if ver.startswith(version):
                return path
        
        return None
    
    return found_versions

def extract_speedtree_version(name_string):
    """
    从字符串中提取SpeedTree版本号
    
    Args:
        name_string (str): 包含版本信息的字符串
    
    Returns:
        str: 版本号，如 "10.0.1"
    """
    import re
    
    # 常见的版本号模式
    patterns = [
        r'(\d+\.\d+\.\d+)',  # 10.0.1
        r'(\d+\.\d+)',       # 10.0
        r'v(\d+\.\d+\.\d+)', # v10.0.1
        r'v(\d+\.\d+)',      # v10.0
        r'(\d+)',            # 10
    ]
    
    for pattern in patterns:
        match = re.search(pattern, name_string)
        if match:
            return match.group(1)
    
    return None

def get_speedtree_10_0_path():
    """
    专门获取SpeedTree 10.0.1的安装路径
    """
    
    # 尝试多种版本号格式
    version_formats = [
        "10.0.1",
        "10.0",
        " 10.0.1",
        " 10.0",
        "10.0.1 ",
        "10.0 ",
        "v10.0.1",
        "v10.0"
    ]
    
    for version_format in version_formats:
        path = get_speedtree_install_path(version_format)
        if path:
            return path
    
    # 如果没找到，列出所有版本让用户选择
    all_versions = get_speedtree_install_path()
    
    print("未找到SpeedTree 10.0.1，以下是找到的所有版本:")
    for ver, path in all_versions.items():
        print(f"  {ver}: {path}")
    
    # 尝试找最接近的版本
    for ver, path in all_versions.items():
        if "10.0" in ver or "10" in ver:
            print(f"使用最接近的版本: {ver}")
            return path
    
    return None

def get_speedtree_executable_path(version=None):
    """
    获取SpeedTree可执行文件路径
    
    Args:
        version (str): 版本号
    
    Returns:
        dict: 包含各种可执行文件路径的字典
    """
    
    install_path = get_speedtree_install_path(version) if version else get_speedtree_10_0_path()
    
    if not install_path:
        return None
    
    install_path = Path(install_path)
    
    executables = {}
    
    # 常见的SpeedTree可执行文件
    exe_files = [
        "SpeedTree.exe",
        "SpeedTreeModeler.exe",
        "SpeedTreeCompiler.exe",
        "SpeedTree Modeler.exe"
    ]
    
    # 在多个可能的位置查找
    search_paths = [
        install_path,
        install_path / "bin",
        install_path / "Bin",
        install_path / "SpeedTree",
    ]
    
    for search_path in search_paths:
        if search_path.exists():
            for exe_file in exe_files:
                exe_path = search_path / exe_file
                if exe_path.exists():
                    exe_name = exe_file.replace(".exe", "")
                    executables[exe_name] = str(exe_path)
    
    return executables

def verify_speedtree_installation(install_path):
    """
    验证SpeedTree安装是否完整
    
    Args:
        install_path (str): 安装路径
    
    Returns:
        dict: 验证结果
    """
    
    if not install_path or not os.path.exists(install_path):
        return {"valid": False, "reason": "路径不存在"}
    
    install_path = Path(install_path)
    
    # 检查关键目录和文件
    checks = {
        "SpeedTree.exe": install_path / "SpeedTree.exe",
        "SpeedTreeModeler.exe": install_path / "SpeedTreeModeler.exe",
        "bin目录": install_path / "bin",
        "Data目录": install_path / "Data",
        "Templates目录": install_path / "Templates"
    }
    
    # 如果主目录没有exe，检查bin目录
    if not (install_path / "SpeedTree.exe").exists():
        bin_path = install_path / "bin"
        if bin_path.exists():
            checks["SpeedTree.exe"] = bin_path / "SpeedTree.exe"
            checks["SpeedTreeModeler.exe"] = bin_path / "SpeedTreeModeler.exe"
    
    results = {}
    all_valid = True
    
    for name, path in checks.items():
        exists = path.exists()
        results[name] = {"path": str(path), "exists": exists}
        if not exists and name.endswith(".exe"):
            all_valid = False
    
    return {
        "valid": all_valid,
        "install_path": str(install_path),
        "checks": results
    }

def main():
    """
    主函数：演示所有功能
    """
    
    print("="*60)
    print("SpeedTree安装路径检测")
    print("="*60)
    
    # 1. 查找所有版本
    print("\n1. 查找所有SpeedTree版本:")
    all_versions = get_speedtree_install_path()
    
    if all_versions:
        for version, path in all_versions.items():
            print(f"   SpeedTree {version}: {path}")
    else:
        print("   未找到任何SpeedTree安装")
        return
    
    # 2. 查找特定版本 (10.0.1)
    print(f"\n2. 查找SpeedTree 10.0.1:")
    target_path = get_speedtree_10_0_path()
    
    if target_path:
        print(f"   找到: {target_path}")
        
        # 3. 验证安装
        print(f"\n3. 验证安装:")
        verification = verify_speedtree_installation(target_path)
        
        if verification["valid"]:
            print("   ✅ 安装完整")
        else:
            print("   ❌ 安装不完整")
        
        for name, info in verification["checks"].items():
            status = "✅" if info["exists"] else "❌"
            print(f"   {status} {name}: {info['path']}")
        
        # 4. 获取可执行文件路径
        print(f"\n4. 可执行文件:")
        executables = get_speedtree_executable_path("10.0")
        
        if executables:
            for name, path in executables.items():
                print(f"   {name}: {path}")
        else:
            print("   未找到可执行文件")
            
    else:
        print("   未找到SpeedTree 10.0.1")
    
    # 5. 使用示例
    print(f"\n5. 使用示例:")
    print("   # 获取特定版本路径")
    print("   path = get_speedtree_install_path('10.0.1')")
    print("   ")
    print("   # 获取所有版本")
    print("   versions = get_speedtree_install_path()")
    print("   ")
    print("   # 获取可执行文件")
    print("   exes = get_speedtree_executable_path('10.0')")

if __name__ == "__main__":
    main()